name: "CodeQL"

on:
  push:
    branches:
      - "main"
      - "v*.*.*"
  pull_request:
    branches:
      - "main"
      - "v*.*.*"

jobs:
  analyze:
    name: Analyze (${{ matrix.language }})
    runs-on: 'ubuntu-latest'
    permissions:
      security-events: write
      packages: read
      actions: read
      contents: read

    strategy:
      fail-fast: false
      matrix:
        include:
          - language: c-cpp
            build-mode: autobuild
          - language: go
            build-mode: autobuild

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libelf-dev clang make gcc pkg-config

    - name: Update submodules
      run: git submodule update --init --recursive

    - name: Initialize CodeQL
      uses: github/codeql-action/init@v3
      with:
        languages: ${{ matrix.language }}
        build-mode: ${{ matrix.build-mode }}

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3
      with:
        category: "/language:${{matrix.language}}"
