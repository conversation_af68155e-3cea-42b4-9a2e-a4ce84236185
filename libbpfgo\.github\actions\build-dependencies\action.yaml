name: Build Dependencies
description: |
  Install build dependencies to test and compile tracee artifacts
inputs:
  go-version:
    description: go version
    default: "1.21"
runs:
  using: composite
  steps:
    - name: Setup Go
      uses: actions/setup-go@cdcb36043654635271a94b9a6d1392de5bb323a7 # v5.0.1
      with:
        go-version: "${{ inputs.go-version }}"
    - name: Install Compilers & Formatters
      run: |
        sudo apt-get update
        sudo apt-get install --yes bsdutils
        sudo apt-get install --yes build-essential
        sudo apt-get install --yes pkgconf
        sudo apt-get install --yes llvm-14 clang-14 clang-format-14
        sudo apt-get install --yes libelf-dev libzstd-dev zlib1g-dev
        sudo apt-get install --yes virtme-ng
        sudo apt-get install --yes gcc-multilib
        sudo apt-get install --yes systemtap-sdt-dev
        for tool in "clang" "clang-format" "llc" "llvm-strip"
        do
          sudo rm -f /usr/bin/$tool
          sudo ln -s /usr/bin/$tool-14 /usr/bin/$tool
        done
      shell: bash
