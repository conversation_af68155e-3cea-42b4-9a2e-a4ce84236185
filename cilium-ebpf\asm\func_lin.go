﻿// Code generated by internal/cmd/genfunctions.awk; DO NOT EDIT.

package asm

// Code in this file is derived from Linux, available under the GPL-2.0 WITH Linux-syscall-note.

import "github.com/cilium/ebpf/internal/platform"

// Built-in functions (Linux).
const (
	FnUnspec                     = BuiltinFunc(platform.LinuxTag | 0) //lint:ignore SA4016 consistency
	FnMapLookupElem              = BuiltinFunc(platform.LinuxTag | 1)
	FnMapUpdateElem              = BuiltinFunc(platform.LinuxTag | 2)
	FnMapDeleteElem              = BuiltinFunc(platform.LinuxTag | 3)
	FnProbeRead                  = BuiltinFunc(platform.LinuxTag | 4)
	FnKtimeGetNs                 = BuiltinFunc(platform.LinuxTag | 5)
	FnTracePrintk                = BuiltinFunc(platform.LinuxTag | 6)
	FnGetPrandomU32              = BuiltinFunc(platform.LinuxTag | 7)
	FnGetSmpProcessorId          = BuiltinFunc(platform.LinuxTag | 8)
	FnSkbStoreBytes              = BuiltinFunc(platform.LinuxTag | 9)
	FnL3CsumReplace              = BuiltinFunc(platform.LinuxTag | 10)
	FnL4CsumReplace              = BuiltinFunc(platform.LinuxTag | 11)
	FnTailCall                   = BuiltinFunc(platform.LinuxTag | 12)
	FnCloneRedirect              = BuiltinFunc(platform.LinuxTag | 13)
	FnGetCurrentPidTgid          = BuiltinFunc(platform.LinuxTag | 14)
	FnGetCurrentUidGid           = BuiltinFunc(platform.LinuxTag | 15)
	FnGetCurrentComm             = BuiltinFunc(platform.LinuxTag | 16)
	FnGetCgroupClassid           = BuiltinFunc(platform.LinuxTag | 17)
	FnSkbVlanPush                = BuiltinFunc(platform.LinuxTag | 18)
	FnSkbVlanPop                 = BuiltinFunc(platform.LinuxTag | 19)
	FnSkbGetTunnelKey            = BuiltinFunc(platform.LinuxTag | 20)
	FnSkbSetTunnelKey            = BuiltinFunc(platform.LinuxTag | 21)
	FnPerfEventRead              = BuiltinFunc(platform.LinuxTag | 22)
	FnRedirect                   = BuiltinFunc(platform.LinuxTag | 23)
	FnGetRouteRealm              = BuiltinFunc(platform.LinuxTag | 24)
	FnPerfEventOutput            = BuiltinFunc(platform.LinuxTag | 25)
	FnSkbLoadBytes               = BuiltinFunc(platform.LinuxTag | 26)
	FnGetStackid                 = BuiltinFunc(platform.LinuxTag | 27)
	FnCsumDiff                   = BuiltinFunc(platform.LinuxTag | 28)
	FnSkbGetTunnelOpt            = BuiltinFunc(platform.LinuxTag | 29)
	FnSkbSetTunnelOpt            = BuiltinFunc(platform.LinuxTag | 30)
	FnSkbChangeProto             = BuiltinFunc(platform.LinuxTag | 31)
	FnSkbChangeType              = BuiltinFunc(platform.LinuxTag | 32)
	FnSkbUnderCgroup             = BuiltinFunc(platform.LinuxTag | 33)
	FnGetHashRecalc              = BuiltinFunc(platform.LinuxTag | 34)
	FnGetCurrentTask             = BuiltinFunc(platform.LinuxTag | 35)
	FnProbeWriteUser             = BuiltinFunc(platform.LinuxTag | 36)
	FnCurrentTaskUnderCgroup     = BuiltinFunc(platform.LinuxTag | 37)
	FnSkbChangeTail              = BuiltinFunc(platform.LinuxTag | 38)
	FnSkbPullData                = BuiltinFunc(platform.LinuxTag | 39)
	FnCsumUpdate                 = BuiltinFunc(platform.LinuxTag | 40)
	FnSetHashInvalid             = BuiltinFunc(platform.LinuxTag | 41)
	FnGetNumaNodeId              = BuiltinFunc(platform.LinuxTag | 42)
	FnSkbChangeHead              = BuiltinFunc(platform.LinuxTag | 43)
	FnXdpAdjustHead              = BuiltinFunc(platform.LinuxTag | 44)
	FnProbeReadStr               = BuiltinFunc(platform.LinuxTag | 45)
	FnGetSocketCookie            = BuiltinFunc(platform.LinuxTag | 46)
	FnGetSocketUid               = BuiltinFunc(platform.LinuxTag | 47)
	FnSetHash                    = BuiltinFunc(platform.LinuxTag | 48)
	FnSetsockopt                 = BuiltinFunc(platform.LinuxTag | 49)
	FnSkbAdjustRoom              = BuiltinFunc(platform.LinuxTag | 50)
	FnRedirectMap                = BuiltinFunc(platform.LinuxTag | 51)
	FnSkRedirectMap              = BuiltinFunc(platform.LinuxTag | 52)
	FnSockMapUpdate              = BuiltinFunc(platform.LinuxTag | 53)
	FnXdpAdjustMeta              = BuiltinFunc(platform.LinuxTag | 54)
	FnPerfEventReadValue         = BuiltinFunc(platform.LinuxTag | 55)
	FnPerfProgReadValue          = BuiltinFunc(platform.LinuxTag | 56)
	FnGetsockopt                 = BuiltinFunc(platform.LinuxTag | 57)
	FnOverrideReturn             = BuiltinFunc(platform.LinuxTag | 58)
	FnSockOpsCbFlagsSet          = BuiltinFunc(platform.LinuxTag | 59)
	FnMsgRedirectMap             = BuiltinFunc(platform.LinuxTag | 60)
	FnMsgApplyBytes              = BuiltinFunc(platform.LinuxTag | 61)
	FnMsgCorkBytes               = BuiltinFunc(platform.LinuxTag | 62)
	FnMsgPullData                = BuiltinFunc(platform.LinuxTag | 63)
	FnBind                       = BuiltinFunc(platform.LinuxTag | 64)
	FnXdpAdjustTail              = BuiltinFunc(platform.LinuxTag | 65)
	FnSkbGetXfrmState            = BuiltinFunc(platform.LinuxTag | 66)
	FnGetStack                   = BuiltinFunc(platform.LinuxTag | 67)
	FnSkbLoadBytesRelative       = BuiltinFunc(platform.LinuxTag | 68)
	FnFibLookup                  = BuiltinFunc(platform.LinuxTag | 69)
	FnSockHashUpdate             = BuiltinFunc(platform.LinuxTag | 70)
	FnMsgRedirectHash            = BuiltinFunc(platform.LinuxTag | 71)
	FnSkRedirectHash             = BuiltinFunc(platform.LinuxTag | 72)
	FnLwtPushEncap               = BuiltinFunc(platform.LinuxTag | 73)
	FnLwtSeg6StoreBytes          = BuiltinFunc(platform.LinuxTag | 74)
	FnLwtSeg6AdjustSrh           = BuiltinFunc(platform.LinuxTag | 75)
	FnLwtSeg6Action              = BuiltinFunc(platform.LinuxTag | 76)
	FnRcRepeat                   = BuiltinFunc(platform.LinuxTag | 77)
	FnRcKeydown                  = BuiltinFunc(platform.LinuxTag | 78)
	FnSkbCgroupId                = BuiltinFunc(platform.LinuxTag | 79)
	FnGetCurrentCgroupId         = BuiltinFunc(platform.LinuxTag | 80)
	FnGetLocalStorage            = BuiltinFunc(platform.LinuxTag | 81)
	FnSkSelectReuseport          = BuiltinFunc(platform.LinuxTag | 82)
	FnSkbAncestorCgroupId        = BuiltinFunc(platform.LinuxTag | 83)
	FnSkLookupTcp                = BuiltinFunc(platform.LinuxTag | 84)
	FnSkLookupUdp                = BuiltinFunc(platform.LinuxTag | 85)
	FnSkRelease                  = BuiltinFunc(platform.LinuxTag | 86)
	FnMapPushElem                = BuiltinFunc(platform.LinuxTag | 87)
	FnMapPopElem                 = BuiltinFunc(platform.LinuxTag | 88)
	FnMapPeekElem                = BuiltinFunc(platform.LinuxTag | 89)
	FnMsgPushData                = BuiltinFunc(platform.LinuxTag | 90)
	FnMsgPopData                 = BuiltinFunc(platform.LinuxTag | 91)
	FnRcPointerRel               = BuiltinFunc(platform.LinuxTag | 92)
	FnSpinLock                   = BuiltinFunc(platform.LinuxTag | 93)
	FnSpinUnlock                 = BuiltinFunc(platform.LinuxTag | 94)
	FnSkFullsock                 = BuiltinFunc(platform.LinuxTag | 95)
	FnTcpSock                    = BuiltinFunc(platform.LinuxTag | 96)
	FnSkbEcnSetCe                = BuiltinFunc(platform.LinuxTag | 97)
	FnGetListenerSock            = BuiltinFunc(platform.LinuxTag | 98)
	FnSkcLookupTcp               = BuiltinFunc(platform.LinuxTag | 99)
	FnTcpCheckSyncookie          = BuiltinFunc(platform.LinuxTag | 100)
	FnSysctlGetName              = BuiltinFunc(platform.LinuxTag | 101)
	FnSysctlGetCurrentValue      = BuiltinFunc(platform.LinuxTag | 102)
	FnSysctlGetNewValue          = BuiltinFunc(platform.LinuxTag | 103)
	FnSysctlSetNewValue          = BuiltinFunc(platform.LinuxTag | 104)
	FnStrtol                     = BuiltinFunc(platform.LinuxTag | 105)
	FnStrtoul                    = BuiltinFunc(platform.LinuxTag | 106)
	FnSkStorageGet               = BuiltinFunc(platform.LinuxTag | 107)
	FnSkStorageDelete            = BuiltinFunc(platform.LinuxTag | 108)
	FnSendSignal                 = BuiltinFunc(platform.LinuxTag | 109)
	FnTcpGenSyncookie            = BuiltinFunc(platform.LinuxTag | 110)
	FnSkbOutput                  = BuiltinFunc(platform.LinuxTag | 111)
	FnProbeReadUser              = BuiltinFunc(platform.LinuxTag | 112)
	FnProbeReadKernel            = BuiltinFunc(platform.LinuxTag | 113)
	FnProbeReadUserStr           = BuiltinFunc(platform.LinuxTag | 114)
	FnProbeReadKernelStr         = BuiltinFunc(platform.LinuxTag | 115)
	FnTcpSendAck                 = BuiltinFunc(platform.LinuxTag | 116)
	FnSendSignalThread           = BuiltinFunc(platform.LinuxTag | 117)
	FnJiffies64                  = BuiltinFunc(platform.LinuxTag | 118)
	FnReadBranchRecords          = BuiltinFunc(platform.LinuxTag | 119)
	FnGetNsCurrentPidTgid        = BuiltinFunc(platform.LinuxTag | 120)
	FnXdpOutput                  = BuiltinFunc(platform.LinuxTag | 121)
	FnGetNetnsCookie             = BuiltinFunc(platform.LinuxTag | 122)
	FnGetCurrentAncestorCgroupId = BuiltinFunc(platform.LinuxTag | 123)
	FnSkAssign                   = BuiltinFunc(platform.LinuxTag | 124)
	FnKtimeGetBootNs             = BuiltinFunc(platform.LinuxTag | 125)
	FnSeqPrintf                  = BuiltinFunc(platform.LinuxTag | 126)
	FnSeqWrite                   = BuiltinFunc(platform.LinuxTag | 127)
	FnSkCgroupId                 = BuiltinFunc(platform.LinuxTag | 128)
	FnSkAncestorCgroupId         = BuiltinFunc(platform.LinuxTag | 129)
	FnRingbufOutput              = BuiltinFunc(platform.LinuxTag | 130)
	FnRingbufReserve             = BuiltinFunc(platform.LinuxTag | 131)
	FnRingbufSubmit              = BuiltinFunc(platform.LinuxTag | 132)
	FnRingbufDiscard             = BuiltinFunc(platform.LinuxTag | 133)
	FnRingbufQuery               = BuiltinFunc(platform.LinuxTag | 134)
	FnCsumLevel                  = BuiltinFunc(platform.LinuxTag | 135)
	FnSkcToTcp6Sock              = BuiltinFunc(platform.LinuxTag | 136)
	FnSkcToTcpSock               = BuiltinFunc(platform.LinuxTag | 137)
	FnSkcToTcpTimewaitSock       = BuiltinFunc(platform.LinuxTag | 138)
	FnSkcToTcpRequestSock        = BuiltinFunc(platform.LinuxTag | 139)
	FnSkcToUdp6Sock              = BuiltinFunc(platform.LinuxTag | 140)
	FnGetTaskStack               = BuiltinFunc(platform.LinuxTag | 141)
	FnLoadHdrOpt                 = BuiltinFunc(platform.LinuxTag | 142)
	FnStoreHdrOpt                = BuiltinFunc(platform.LinuxTag | 143)
	FnReserveHdrOpt              = BuiltinFunc(platform.LinuxTag | 144)
	FnInodeStorageGet            = BuiltinFunc(platform.LinuxTag | 145)
	FnInodeStorageDelete         = BuiltinFunc(platform.LinuxTag | 146)
	FnDPath                      = BuiltinFunc(platform.LinuxTag | 147)
	FnCopyFromUser               = BuiltinFunc(platform.LinuxTag | 148)
	FnSnprintfBtf                = BuiltinFunc(platform.LinuxTag | 149)
	FnSeqPrintfBtf               = BuiltinFunc(platform.LinuxTag | 150)
	FnSkbCgroupClassid           = BuiltinFunc(platform.LinuxTag | 151)
	FnRedirectNeigh              = BuiltinFunc(platform.LinuxTag | 152)
	FnPerCpuPtr                  = BuiltinFunc(platform.LinuxTag | 153)
	FnThisCpuPtr                 = BuiltinFunc(platform.LinuxTag | 154)
	FnRedirectPeer               = BuiltinFunc(platform.LinuxTag | 155)
	FnTaskStorageGet             = BuiltinFunc(platform.LinuxTag | 156)
	FnTaskStorageDelete          = BuiltinFunc(platform.LinuxTag | 157)
	FnGetCurrentTaskBtf          = BuiltinFunc(platform.LinuxTag | 158)
	FnBprmOptsSet                = BuiltinFunc(platform.LinuxTag | 159)
	FnKtimeGetCoarseNs           = BuiltinFunc(platform.LinuxTag | 160)
	FnImaInodeHash               = BuiltinFunc(platform.LinuxTag | 161)
	FnSockFromFile               = BuiltinFunc(platform.LinuxTag | 162)
	FnCheckMtu                   = BuiltinFunc(platform.LinuxTag | 163)
	FnForEachMapElem             = BuiltinFunc(platform.LinuxTag | 164)
	FnSnprintf                   = BuiltinFunc(platform.LinuxTag | 165)
	FnSysBpf                     = BuiltinFunc(platform.LinuxTag | 166)
	FnBtfFindByNameKind          = BuiltinFunc(platform.LinuxTag | 167)
	FnSysClose                   = BuiltinFunc(platform.LinuxTag | 168)
	FnTimerInit                  = BuiltinFunc(platform.LinuxTag | 169)
	FnTimerSetCallback           = BuiltinFunc(platform.LinuxTag | 170)
	FnTimerStart                 = BuiltinFunc(platform.LinuxTag | 171)
	FnTimerCancel                = BuiltinFunc(platform.LinuxTag | 172)
	FnGetFuncIp                  = BuiltinFunc(platform.LinuxTag | 173)
	FnGetAttachCookie            = BuiltinFunc(platform.LinuxTag | 174)
	FnTaskPtRegs                 = BuiltinFunc(platform.LinuxTag | 175)
	FnGetBranchSnapshot          = BuiltinFunc(platform.LinuxTag | 176)
	FnTraceVprintk               = BuiltinFunc(platform.LinuxTag | 177)
	FnSkcToUnixSock              = BuiltinFunc(platform.LinuxTag | 178)
	FnKallsymsLookupName         = BuiltinFunc(platform.LinuxTag | 179)
	FnFindVma                    = BuiltinFunc(platform.LinuxTag | 180)
	FnLoop                       = BuiltinFunc(platform.LinuxTag | 181)
	FnStrncmp                    = BuiltinFunc(platform.LinuxTag | 182)
	FnGetFuncArg                 = BuiltinFunc(platform.LinuxTag | 183)
	FnGetFuncRet                 = BuiltinFunc(platform.LinuxTag | 184)
	FnGetFuncArgCnt              = BuiltinFunc(platform.LinuxTag | 185)
	FnGetRetval                  = BuiltinFunc(platform.LinuxTag | 186)
	FnSetRetval                  = BuiltinFunc(platform.LinuxTag | 187)
	FnXdpGetBuffLen              = BuiltinFunc(platform.LinuxTag | 188)
	FnXdpLoadBytes               = BuiltinFunc(platform.LinuxTag | 189)
	FnXdpStoreBytes              = BuiltinFunc(platform.LinuxTag | 190)
	FnCopyFromUserTask           = BuiltinFunc(platform.LinuxTag | 191)
	FnSkbSetTstamp               = BuiltinFunc(platform.LinuxTag | 192)
	FnImaFileHash                = BuiltinFunc(platform.LinuxTag | 193)
	FnKptrXchg                   = BuiltinFunc(platform.LinuxTag | 194)
	FnMapLookupPercpuElem        = BuiltinFunc(platform.LinuxTag | 195)
	FnSkcToMptcpSock             = BuiltinFunc(platform.LinuxTag | 196)
	FnDynptrFromMem              = BuiltinFunc(platform.LinuxTag | 197)
	FnRingbufReserveDynptr       = BuiltinFunc(platform.LinuxTag | 198)
	FnRingbufSubmitDynptr        = BuiltinFunc(platform.LinuxTag | 199)
	FnRingbufDiscardDynptr       = BuiltinFunc(platform.LinuxTag | 200)
	FnDynptrRead                 = BuiltinFunc(platform.LinuxTag | 201)
	FnDynptrWrite                = BuiltinFunc(platform.LinuxTag | 202)
	FnDynptrData                 = BuiltinFunc(platform.LinuxTag | 203)
	FnTcpRawGenSyncookieIpv4     = BuiltinFunc(platform.LinuxTag | 204)
	FnTcpRawGenSyncookieIpv6     = BuiltinFunc(platform.LinuxTag | 205)
	FnTcpRawCheckSyncookieIpv4   = BuiltinFunc(platform.LinuxTag | 206)
	FnTcpRawCheckSyncookieIpv6   = BuiltinFunc(platform.LinuxTag | 207)
	FnKtimeGetTaiNs              = BuiltinFunc(platform.LinuxTag | 208)
	FnUserRingbufDrain           = BuiltinFunc(platform.LinuxTag | 209)
	FnCgrpStorageGet             = BuiltinFunc(platform.LinuxTag | 210)
	FnCgrpStorageDelete          = BuiltinFunc(platform.LinuxTag | 211)
)
