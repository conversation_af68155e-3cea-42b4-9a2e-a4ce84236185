name: PR
on:
  workflow_dispatch: {}
  pull_request:
    branches:
      - main
  workflow_call:
jobs:
  analyze-code:
    name: Analyze Code
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout Code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Install Dependencies
        uses: ./.github/actions/build-dependencies
      - name: Lint
        run: |
          if test -z "$(gofmt -l .)"; then
            echo "Congrats! There is nothing to fix."
          else
            echo "The following lines should be fixed."
            gofmt -s -d .
            exit 1
          fi
        shell: bash
      # - name: Install staticchecker
      #   run: |
      #     go install honnef.co/go/tools/cmd/staticcheck@latest
      #     cp $HOME/go/bin/staticcheck /usr/bin/
      #   shell: bash
      - name: Install revive
        run: |
          go install github.com/mgechev/revive@e33fb87
          sudo cp $HOME/go/bin/revive /usr/bin/
        shell: bash
      # - name: Install goimports-reviser
      #   run: |
      #     go go install github.com/incu6us/goimports-reviser/v3@latest
      #     sudo cp $HOME/go/bin/goimports-reviser /usr/bin/
      #   shell: bash
      # - name: Install errcheck
      #   run: |
      #     go install github.com/kisielk/errcheck@latest
      #     sudo cp $HOME/go/bin/errcheck /usr/bin/
      #   shell: bash
      - name: Check Code Style
        run: |
          make fmt-check
        shell: bash
      - name: Lint (Revive)
        run: |
          make lint-check
        shell: bash
  libbpfgo-unit-tests:
    name: libbpfgo Unit Tests
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        go-version: [ '1.18', '1.19', '1.20', '1.21', '1.22', '1.23', 'stable' ]
    steps:
      - name: Checkout Code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Install Dependencies
        uses: ./.github/actions/build-dependencies
        with:
          go-version: ${{ matrix.go-version }}
      - name: Test libbpfgo
        run: |
          make libbpfgo-static-test
  helpers-unit-tests:
    name: Helpers Unit Tests
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        go-version: [ '1.18', '1.19', '1.20', '1.21', '1.22', '1.23', 'stable' ]
    steps:
      - name: Checkout Code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Install Dependencies
        uses: ./.github/actions/build-dependencies
        with:
          go-version: ${{ matrix.go-version }}
      - name: Test Helpers
        run: |
          make helpers-test-static-run
  self-tests:
    name: Selftests
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        go-version: [ '1.18', '1.19', '1.20', '1.21', '1.22', '1.23', 'stable' ]
    steps:
      - name: Checkout Code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Install Dependencies
        uses: ./.github/actions/build-dependencies
        with:
          go-version: ${{ matrix.go-version }}
      - name: Static Selftests
        run: |
          make selftest-static-run
