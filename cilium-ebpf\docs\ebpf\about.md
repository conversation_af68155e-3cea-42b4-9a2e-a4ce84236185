The project was initially created in 2017 as
[`newtools/ebpf`](https://github.com/newtools/ebpf) by a group of passionate
developers wanting to bring the power eBPF to Go applications. It quickly gained
traction within the Go community, especially for projects that couldn't or
wouldn't build upon the CGo-based BCC bindings at the time (`gobpf`).

Since its inception, {{ proj }} has seen remarkable growth and widespread
adoption. It has become a fundamental building block for numerous open-source
projects. Major industry players and forward-thinking startups have integrated
the library into their technology stacks to combine the power and flexibility of
eBPF with the iteration speed, runtime safety and ease of deployment provided by
the Go language.

{{ proj }} maintains a strong commitment to collaborating with the upstream
Linux project, which ensures that it stays aligned with the latest advancements
in the eBPF ecosystem and remains compatible with the evolving Linux kernel and
its co-located BPF library, `libbpf`.

Thank you for being a part of our :ebee-color: eBPF journey!
