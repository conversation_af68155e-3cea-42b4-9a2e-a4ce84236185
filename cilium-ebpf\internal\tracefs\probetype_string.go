// Code generated by "stringer -type=ProbeType -linecomment"; DO NOT EDIT.

package tracefs

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[Kprobe-0]
	_ = x[Uprobe-1]
}

const _ProbeType_name = "kprobeuprobe"

var _ProbeType_index = [...]uint8{0, 6, 12}

func (i ProbeType) String() string {
	if i >= ProbeType(len(_ProbeType_index)-1) {
		return "ProbeType(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _ProbeType_name[_ProbeType_index[i]:_ProbeType_index[i+1]]
}
