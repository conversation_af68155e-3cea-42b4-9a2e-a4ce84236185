// Code generated by "stringer -tags windows -output result_string_windows.go -type=Result"; DO NOT EDIT.

package efw

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[EBPF_SUCCESS-0]
	_ = x[EBPF_VERIFICATION_FAILED-1]
	_ = x[EBPF_JIT_COMPILATION_FAILED-2]
	_ = x[EBPF_PROGRAM_LOAD_FAILED-3]
	_ = x[EBPF_INVALID_FD-4]
	_ = x[EBPF_INVALID_OBJECT-5]
	_ = x[EBPF_INVALID_ARGUMENT-6]
	_ = x[EBPF_OBJECT_NOT_FOUND-7]
	_ = x[EBPF_OBJECT_ALREADY_EXISTS-8]
	_ = x[EBPF_FILE_NOT_FOUND-9]
	_ = x[EBPF_ALREADY_PINNED-10]
	_ = x[EBPF_NOT_PINNED-11]
	_ = x[EBPF_NO_MEMORY-12]
	_ = x[EBPF_PROGRAM_TOO_LARGE-13]
	_ = x[EBPF_RPC_EXCEPTION-14]
	_ = x[EBPF_ALREADY_INITIALIZED-15]
	_ = x[EBPF_ELF_PARSING_FAILED-16]
	_ = x[EBPF_FAILED-17]
	_ = x[EBPF_OPERATION_NOT_SUPPORTED-18]
	_ = x[EBPF_KEY_NOT_FOUND-19]
	_ = x[EBPF_ACCESS_DENIED-20]
	_ = x[EBPF_BLOCKED_BY_POLICY-21]
	_ = x[EBPF_ARITHMETIC_OVERFLOW-22]
	_ = x[EBPF_EXTENSION_FAILED_TO_LOAD-23]
	_ = x[EBPF_INSUFFICIENT_BUFFER-24]
	_ = x[EBPF_NO_MORE_KEYS-25]
	_ = x[EBPF_KEY_ALREADY_EXISTS-26]
	_ = x[EBPF_NO_MORE_TAIL_CALLS-27]
	_ = x[EBPF_PENDING-28]
	_ = x[EBPF_OUT_OF_SPACE-29]
	_ = x[EBPF_CANCELED-30]
	_ = x[EBPF_INVALID_POINTER-31]
	_ = x[EBPF_TIMEOUT-32]
	_ = x[EBPF_STALE_ID-33]
	_ = x[EBPF_INVALID_STATE-34]
}

const _Result_name = "EBPF_SUCCESSEBPF_VERIFICATION_FAILEDEBPF_JIT_COMPILATION_FAILEDEBPF_PROGRAM_LOAD_FAILEDEBPF_INVALID_FDEBPF_INVALID_OBJECTEBPF_INVALID_ARGUMENTEBPF_OBJECT_NOT_FOUNDEBPF_OBJECT_ALREADY_EXISTSEBPF_FILE_NOT_FOUNDEBPF_ALREADY_PINNEDEBPF_NOT_PINNEDEBPF_NO_MEMORYEBPF_PROGRAM_TOO_LARGEEBPF_RPC_EXCEPTIONEBPF_ALREADY_INITIALIZEDEBPF_ELF_PARSING_FAILEDEBPF_FAILEDEBPF_OPERATION_NOT_SUPPORTEDEBPF_KEY_NOT_FOUNDEBPF_ACCESS_DENIEDEBPF_BLOCKED_BY_POLICYEBPF_ARITHMETIC_OVERFLOWEBPF_EXTENSION_FAILED_TO_LOADEBPF_INSUFFICIENT_BUFFEREBPF_NO_MORE_KEYSEBPF_KEY_ALREADY_EXISTSEBPF_NO_MORE_TAIL_CALLSEBPF_PENDINGEBPF_OUT_OF_SPACEEBPF_CANCELEDEBPF_INVALID_POINTEREBPF_TIMEOUTEBPF_STALE_IDEBPF_INVALID_STATE"

var _Result_index = [...]uint16{0, 12, 36, 63, 87, 102, 121, 142, 163, 189, 208, 227, 242, 256, 278, 296, 320, 343, 354, 382, 400, 418, 440, 464, 493, 517, 534, 557, 580, 592, 609, 622, 642, 654, 667, 685}

func (i Result) String() string {
	if i < 0 || i >= Result(len(_Result_index)-1) {
		return "Result(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _Result_name[_Result_index[i]:_Result_index[i+1]]
}
