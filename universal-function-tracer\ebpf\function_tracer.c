/* SPDX-License-Identifier: GPL-2.0 */
/*
 * Universal eBPF Function Tracer - Main eBPF Program
 * 
 * This program implements function-level tracing using proven patterns
 * from the tracee project. It focuses on minimal, working implementation
 * with exact binary layout matching between kernel and userspace.
 * 
 * Key Features:
 * - Proven kprobe attachment points (do_sys_openat2)
 * - Ring buffer for efficient event communication
 * - Exact binary structure matching with userspace
 * - Comprehensive error handling and statistics
 * 
 * Success Criteria:
 * - Capture and decode at least 1 event per second
 * - Show actual function events (not "0 events")
 */

#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>
#include <bpf/bpf_core_read.h>
#include "common/types.h"
#include "common/maps.h"
#include "common/helpers.h"

char LICENSE[] SEC("license") = "GPL";

/*
 * Kprobe: do_sys_openat2
 * 
 * This is a PROVEN attachment point that reliably triggers on file operations.
 * It's used by tracee and known to work across different kernel versions.
 * 
 * Triggers on: open(), openat(), openat2() system calls
 * Frequency: High (every file access)
 * Reliability: Excellent
 */
SEC("kprobe/do_sys_openat2")
int trace_do_sys_openat2_entry(struct pt_regs *ctx)
{
    // Use the helper function to submit the event
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

/*
 * Kretprobe: do_sys_openat2
 * 
 * Captures function exit events for correlation with entry events.
 * This allows us to measure function execution time and track
 * function call completion.
 */
SEC("kretprobe/do_sys_openat2")
int trace_do_sys_openat2_exit(struct pt_regs *ctx)
{
    // Use the helper function to submit the event
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_EXIT);
}

/*
 * USER SPACE FUNCTION TRACING - UPROBES
 * These attach to actual user functions that clients want to see
 */

// Trace malloc() calls in libc - REAL USER FUNCTION
SEC("uprobe/libc.so.6:malloc")
int trace_malloc_entry(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

SEC("uretprobe/libc.so.6:malloc")
int trace_malloc_exit(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_EXIT);
}

// Trace free() calls in libc - REAL USER FUNCTION
SEC("uprobe/libc.so.6:free")
int trace_free_entry(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

// Trace printf() calls in libc - REAL USER FUNCTION
SEC("uprobe/libc.so.6:printf")
int trace_printf_entry(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

SEC("uretprobe/libc.so.6:printf")
int trace_printf_exit(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_EXIT);
}

// Trace write() calls in libc - REAL USER FUNCTION (Python uses this)
SEC("uprobe/libc.so.6:write")
int trace_write_entry(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

// Trace read() calls in libc - REAL USER FUNCTION (Python uses this)
SEC("uprobe/libc.so.6:read")
int trace_read_entry(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

/*
 * USERLAND FUNCTION UPROBES - BCC-STYLE APPROACH
 * Based on BCC and tracee research: start with C programs, then extend to other languages
 * These capture real userland function calls, not kernel functions
 */

// C hello world program uprobes - GUARANTEED TO WORK (static symbols)
SEC("uprobe/hello-world:main")
int trace_c_hello_main(struct pt_regs *ctx)
{
    // This captures our C hello world main function
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

SEC("uprobe/hello-world:my_hello_function")
int trace_c_hello_function(struct pt_regs *ctx)
{
    // This captures our custom C function
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

SEC("uprobe/hello-world:my_calculation_function")
int trace_c_calculation_function(struct pt_regs *ctx)
{
    // This captures our custom C calculation function
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

// Printf uprobe - captures ALL printf calls from ANY language (userland function)
SEC("uprobe/libc.so.6:printf")
int trace_userland_printf_entry(struct pt_regs *ctx)
{
    // This captures userland printf calls (not kernel printk)
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

// Puts uprobe - captures Python print() calls (userland function)
SEC("uprobe/libc.so.6:puts")
int trace_userland_puts_entry(struct pt_regs *ctx)
{
    // This captures Python print() which calls puts() in userland
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

// Malloc uprobe - captures memory allocation (userland function)
SEC("uprobe/libc.so.6:malloc")
int trace_userland_malloc_entry(struct pt_regs *ctx)
{
    // This captures userland malloc calls
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

// Free uprobe - captures memory deallocation (userland function)
SEC("uprobe/libc.so.6:free")
int trace_userland_free_entry(struct pt_regs *ctx)
{
    // This captures userland free calls
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

SEC("uretprobe/libpython3.12.so.1.0:PyObject_Call")
int trace_python_object_call_exit(struct pt_regs *ctx)
{
    // Only trace if this is a Python process
    char comm[16];
    bpf_get_current_comm(comm, sizeof(comm));

    // Check if this is python3
    if (!(comm[0] == 'p' && comm[1] == 'y' && comm[2] == 't' && comm[3] == 'h')) {
        return 0;
    }

    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_EXIT);
}

// PyEval_EvalFrameEx - captures Python frame evaluation (function execution)
SEC("uprobe/libpython3.12.so.1.0:PyEval_EvalFrameEx")
int trace_python_eval_frame_entry(struct pt_regs *ctx)
{
    // Only trace if this is a Python process
    char comm[16];
    bpf_get_current_comm(comm, sizeof(comm));

    // Check if this is python3
    if (!(comm[0] == 'p' && comm[1] == 'y' && comm[2] == 't' && comm[3] == 'h')) {
        return 0;
    }

    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

SEC("uretprobe/libpython3.12.so.1.0:PyEval_EvalFrameEx")
int trace_python_eval_frame_exit(struct pt_regs *ctx)
{
    // Only trace if this is a Python process
    char comm[16];
    bpf_get_current_comm(comm, sizeof(comm));

    // Check if this is python3
    if (!(comm[0] == 'p' && comm[1] == 'y' && comm[2] == 't' && comm[3] == 'h')) {
        return 0;
    }

    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_EXIT);
}

/*
 * Kprobe: wake_up_process
 *
 * Another proven attachment point that provides moderate frequency
 * events related to process scheduling and wakeup operations.
 */
SEC("kprobe/wake_up_process")
int trace_wake_up_process_entry(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

/*
 * Network System Calls - TCP/UDP Operations
 */

SEC("kprobe/tcp_sendmsg")
int trace_tcp_sendmsg_entry(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

SEC("kretprobe/tcp_sendmsg")
int trace_tcp_sendmsg_exit(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_EXIT);
}

SEC("kprobe/tcp_recvmsg")
int trace_tcp_recvmsg_entry(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

SEC("kretprobe/tcp_recvmsg")
int trace_tcp_recvmsg_exit(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_EXIT);
}

SEC("kprobe/udp_sendmsg")
int trace_udp_sendmsg_entry(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

SEC("kretprobe/udp_sendmsg")
int trace_udp_sendmsg_exit(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_EXIT);
}

/*
 * Memory Management Operations
 */

SEC("kprobe/do_mmap")
int trace_do_mmap_entry(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

SEC("kretprobe/do_mmap")
int trace_do_mmap_exit(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_EXIT);
}

SEC("kprobe/do_munmap")
int trace_do_munmap_entry(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

SEC("kretprobe/do_munmap")
int trace_do_munmap_exit(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_EXIT);
}

/*
 * Process Management Operations
 */

SEC("kprobe/do_fork")
int trace_do_fork_entry(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

SEC("kretprobe/do_fork")
int trace_do_fork_exit(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_EXIT);
}

SEC("kprobe/do_exit")
int trace_do_exit_entry(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

/*
 * File System Operations (Extended)
 */

SEC("kprobe/vfs_read")
int trace_vfs_read_entry(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

SEC("kretprobe/vfs_read")
int trace_vfs_read_exit(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_EXIT);
}

SEC("kprobe/vfs_write")
int trace_vfs_write_entry(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

SEC("kretprobe/vfs_write")
int trace_vfs_write_exit(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_EXIT);
}

/*
 * Security and Permission Checks
 */

SEC("kprobe/security_file_permission")
int trace_security_file_permission_entry(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_ENTRY);
}

SEC("kretprobe/security_file_permission")
int trace_security_file_permission_exit(struct pt_regs *ctx)
{
    return submit_function_event(ctx, EVENT_TYPE_FUNCTION_EXIT);
}
