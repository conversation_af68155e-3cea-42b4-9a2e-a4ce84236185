// Code generated by "stringer -type=Errno -tags=windows -output=errno_string_windows.go"; DO NOT EDIT.

package unix

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[EPERM-1]
	_ = x[ENOENT-2]
	_ = x[ESRCH-3]
	_ = x[EINTR-4]
	_ = x[E2BIG-7]
	_ = x[EBADF-9]
	_ = x[EAGAIN-11]
	_ = x[EACCES-13]
	_ = x[EFAULT-14]
	_ = x[EEXIST-17]
	_ = x[ENODEV-19]
	_ = x[EINVAL-22]
	_ = x[ENOSPC-28]
	_ = x[EILSEQ-42]
	_ = x[ENOTSUP-129]
	_ = x[EOPNOTSUPP-130]
	_ = x[ENOTSUPP-536870912]
	_ = x[ESTALE-536870913]
}

const _Errno_name = "EPERMENOENTESRCHEINTRE2BIGEBADFEAGAINEACCESEFAULTEEXISTENODEVEINVALENOSPCEILSEQENOTSUPEOPNOTSUPPENOTSUPPESTALE"

var _Errno_map = map[Errno]string{
	1:         _Errno_name[0:5],
	2:         _Errno_name[5:11],
	3:         _Errno_name[11:16],
	4:         _Errno_name[16:21],
	7:         _Errno_name[21:26],
	9:         _Errno_name[26:31],
	11:        _Errno_name[31:37],
	13:        _Errno_name[37:43],
	14:        _Errno_name[43:49],
	17:        _Errno_name[49:55],
	19:        _Errno_name[55:61],
	22:        _Errno_name[61:67],
	28:        _Errno_name[67:73],
	42:        _Errno_name[73:79],
	129:       _Errno_name[79:86],
	130:       _Errno_name[86:96],
	536870912: _Errno_name[96:104],
	536870913: _Errno_name[104:110],
}

func (i Errno) String() string {
	if str, ok := _Errno_map[i]; ok {
		return str
	}
	return "Errno(" + strconv.FormatInt(int64(i), 10) + ")"
}
