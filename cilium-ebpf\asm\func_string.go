// Code generated by "stringer -output func_string.go -type=BuiltinFunc"; DO NOT EDIT.

package asm

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[FnUnspec-0]
	_ = x[FnMapLookupElem-1]
	_ = x[FnMapUpdateElem-2]
	_ = x[FnMapDeleteElem-3]
	_ = x[FnProbeRead-4]
	_ = x[FnKtimeGetNs-5]
	_ = x[FnTracePrintk-6]
	_ = x[FnGetPrandomU32-7]
	_ = x[FnGetSmpProcessorId-8]
	_ = x[FnSkbStoreBytes-9]
	_ = x[FnL3CsumReplace-10]
	_ = x[FnL4CsumReplace-11]
	_ = x[FnTailCall-12]
	_ = x[FnCloneRedirect-13]
	_ = x[FnGetCurrentPidTgid-14]
	_ = x[FnGetCurrentUidGid-15]
	_ = x[FnGetCurrentComm-16]
	_ = x[FnGetCgroupClassid-17]
	_ = x[FnSkbVlanPush-18]
	_ = x[FnSkbVlanPop-19]
	_ = x[FnSkbGetTunnelKey-20]
	_ = x[FnSkbSetTunnelKey-21]
	_ = x[FnPerfEventRead-22]
	_ = x[FnRedirect-23]
	_ = x[FnGetRouteRealm-24]
	_ = x[FnPerfEventOutput-25]
	_ = x[FnSkbLoadBytes-26]
	_ = x[FnGetStackid-27]
	_ = x[FnCsumDiff-28]
	_ = x[FnSkbGetTunnelOpt-29]
	_ = x[FnSkbSetTunnelOpt-30]
	_ = x[FnSkbChangeProto-31]
	_ = x[FnSkbChangeType-32]
	_ = x[FnSkbUnderCgroup-33]
	_ = x[FnGetHashRecalc-34]
	_ = x[FnGetCurrentTask-35]
	_ = x[FnProbeWriteUser-36]
	_ = x[FnCurrentTaskUnderCgroup-37]
	_ = x[FnSkbChangeTail-38]
	_ = x[FnSkbPullData-39]
	_ = x[FnCsumUpdate-40]
	_ = x[FnSetHashInvalid-41]
	_ = x[FnGetNumaNodeId-42]
	_ = x[FnSkbChangeHead-43]
	_ = x[FnXdpAdjustHead-44]
	_ = x[FnProbeReadStr-45]
	_ = x[FnGetSocketCookie-46]
	_ = x[FnGetSocketUid-47]
	_ = x[FnSetHash-48]
	_ = x[FnSetsockopt-49]
	_ = x[FnSkbAdjustRoom-50]
	_ = x[FnRedirectMap-51]
	_ = x[FnSkRedirectMap-52]
	_ = x[FnSockMapUpdate-53]
	_ = x[FnXdpAdjustMeta-54]
	_ = x[FnPerfEventReadValue-55]
	_ = x[FnPerfProgReadValue-56]
	_ = x[FnGetsockopt-57]
	_ = x[FnOverrideReturn-58]
	_ = x[FnSockOpsCbFlagsSet-59]
	_ = x[FnMsgRedirectMap-60]
	_ = x[FnMsgApplyBytes-61]
	_ = x[FnMsgCorkBytes-62]
	_ = x[FnMsgPullData-63]
	_ = x[FnBind-64]
	_ = x[FnXdpAdjustTail-65]
	_ = x[FnSkbGetXfrmState-66]
	_ = x[FnGetStack-67]
	_ = x[FnSkbLoadBytesRelative-68]
	_ = x[FnFibLookup-69]
	_ = x[FnSockHashUpdate-70]
	_ = x[FnMsgRedirectHash-71]
	_ = x[FnSkRedirectHash-72]
	_ = x[FnLwtPushEncap-73]
	_ = x[FnLwtSeg6StoreBytes-74]
	_ = x[FnLwtSeg6AdjustSrh-75]
	_ = x[FnLwtSeg6Action-76]
	_ = x[FnRcRepeat-77]
	_ = x[FnRcKeydown-78]
	_ = x[FnSkbCgroupId-79]
	_ = x[FnGetCurrentCgroupId-80]
	_ = x[FnGetLocalStorage-81]
	_ = x[FnSkSelectReuseport-82]
	_ = x[FnSkbAncestorCgroupId-83]
	_ = x[FnSkLookupTcp-84]
	_ = x[FnSkLookupUdp-85]
	_ = x[FnSkRelease-86]
	_ = x[FnMapPushElem-87]
	_ = x[FnMapPopElem-88]
	_ = x[FnMapPeekElem-89]
	_ = x[FnMsgPushData-90]
	_ = x[FnMsgPopData-91]
	_ = x[FnRcPointerRel-92]
	_ = x[FnSpinLock-93]
	_ = x[FnSpinUnlock-94]
	_ = x[FnSkFullsock-95]
	_ = x[FnTcpSock-96]
	_ = x[FnSkbEcnSetCe-97]
	_ = x[FnGetListenerSock-98]
	_ = x[FnSkcLookupTcp-99]
	_ = x[FnTcpCheckSyncookie-100]
	_ = x[FnSysctlGetName-101]
	_ = x[FnSysctlGetCurrentValue-102]
	_ = x[FnSysctlGetNewValue-103]
	_ = x[FnSysctlSetNewValue-104]
	_ = x[FnStrtol-105]
	_ = x[FnStrtoul-106]
	_ = x[FnSkStorageGet-107]
	_ = x[FnSkStorageDelete-108]
	_ = x[FnSendSignal-109]
	_ = x[FnTcpGenSyncookie-110]
	_ = x[FnSkbOutput-111]
	_ = x[FnProbeReadUser-112]
	_ = x[FnProbeReadKernel-113]
	_ = x[FnProbeReadUserStr-114]
	_ = x[FnProbeReadKernelStr-115]
	_ = x[FnTcpSendAck-116]
	_ = x[FnSendSignalThread-117]
	_ = x[FnJiffies64-118]
	_ = x[FnReadBranchRecords-119]
	_ = x[FnGetNsCurrentPidTgid-120]
	_ = x[FnXdpOutput-121]
	_ = x[FnGetNetnsCookie-122]
	_ = x[FnGetCurrentAncestorCgroupId-123]
	_ = x[FnSkAssign-124]
	_ = x[FnKtimeGetBootNs-125]
	_ = x[FnSeqPrintf-126]
	_ = x[FnSeqWrite-127]
	_ = x[FnSkCgroupId-128]
	_ = x[FnSkAncestorCgroupId-129]
	_ = x[FnRingbufOutput-130]
	_ = x[FnRingbufReserve-131]
	_ = x[FnRingbufSubmit-132]
	_ = x[FnRingbufDiscard-133]
	_ = x[FnRingbufQuery-134]
	_ = x[FnCsumLevel-135]
	_ = x[FnSkcToTcp6Sock-136]
	_ = x[FnSkcToTcpSock-137]
	_ = x[FnSkcToTcpTimewaitSock-138]
	_ = x[FnSkcToTcpRequestSock-139]
	_ = x[FnSkcToUdp6Sock-140]
	_ = x[FnGetTaskStack-141]
	_ = x[FnLoadHdrOpt-142]
	_ = x[FnStoreHdrOpt-143]
	_ = x[FnReserveHdrOpt-144]
	_ = x[FnInodeStorageGet-145]
	_ = x[FnInodeStorageDelete-146]
	_ = x[FnDPath-147]
	_ = x[FnCopyFromUser-148]
	_ = x[FnSnprintfBtf-149]
	_ = x[FnSeqPrintfBtf-150]
	_ = x[FnSkbCgroupClassid-151]
	_ = x[FnRedirectNeigh-152]
	_ = x[FnPerCpuPtr-153]
	_ = x[FnThisCpuPtr-154]
	_ = x[FnRedirectPeer-155]
	_ = x[FnTaskStorageGet-156]
	_ = x[FnTaskStorageDelete-157]
	_ = x[FnGetCurrentTaskBtf-158]
	_ = x[FnBprmOptsSet-159]
	_ = x[FnKtimeGetCoarseNs-160]
	_ = x[FnImaInodeHash-161]
	_ = x[FnSockFromFile-162]
	_ = x[FnCheckMtu-163]
	_ = x[FnForEachMapElem-164]
	_ = x[FnSnprintf-165]
	_ = x[FnSysBpf-166]
	_ = x[FnBtfFindByNameKind-167]
	_ = x[FnSysClose-168]
	_ = x[FnTimerInit-169]
	_ = x[FnTimerSetCallback-170]
	_ = x[FnTimerStart-171]
	_ = x[FnTimerCancel-172]
	_ = x[FnGetFuncIp-173]
	_ = x[FnGetAttachCookie-174]
	_ = x[FnTaskPtRegs-175]
	_ = x[FnGetBranchSnapshot-176]
	_ = x[FnTraceVprintk-177]
	_ = x[FnSkcToUnixSock-178]
	_ = x[FnKallsymsLookupName-179]
	_ = x[FnFindVma-180]
	_ = x[FnLoop-181]
	_ = x[FnStrncmp-182]
	_ = x[FnGetFuncArg-183]
	_ = x[FnGetFuncRet-184]
	_ = x[FnGetFuncArgCnt-185]
	_ = x[FnGetRetval-186]
	_ = x[FnSetRetval-187]
	_ = x[FnXdpGetBuffLen-188]
	_ = x[FnXdpLoadBytes-189]
	_ = x[FnXdpStoreBytes-190]
	_ = x[FnCopyFromUserTask-191]
	_ = x[FnSkbSetTstamp-192]
	_ = x[FnImaFileHash-193]
	_ = x[FnKptrXchg-194]
	_ = x[FnMapLookupPercpuElem-195]
	_ = x[FnSkcToMptcpSock-196]
	_ = x[FnDynptrFromMem-197]
	_ = x[FnRingbufReserveDynptr-198]
	_ = x[FnRingbufSubmitDynptr-199]
	_ = x[FnRingbufDiscardDynptr-200]
	_ = x[FnDynptrRead-201]
	_ = x[FnDynptrWrite-202]
	_ = x[FnDynptrData-203]
	_ = x[FnTcpRawGenSyncookieIpv4-204]
	_ = x[FnTcpRawGenSyncookieIpv6-205]
	_ = x[FnTcpRawCheckSyncookieIpv4-206]
	_ = x[FnTcpRawCheckSyncookieIpv6-207]
	_ = x[FnKtimeGetTaiNs-208]
	_ = x[FnUserRingbufDrain-209]
	_ = x[FnCgrpStorageGet-210]
	_ = x[FnCgrpStorageDelete-211]
	_ = x[WindowsFnMapLookupElem-268435457]
	_ = x[WindowsFnMapUpdateElem-268435458]
	_ = x[WindowsFnMapDeleteElem-268435459]
	_ = x[WindowsFnMapLookupAndDeleteElem-268435460]
	_ = x[WindowsFnTailCall-268435461]
	_ = x[WindowsFnGetPrandomU32-268435462]
	_ = x[WindowsFnKtimeGetBootNs-268435463]
	_ = x[WindowsFnGetSmpProcessorId-268435464]
	_ = x[WindowsFnKtimeGetNs-268435465]
	_ = x[WindowsFnCsumDiff-268435466]
	_ = x[WindowsFnRingbufOutput-268435467]
	_ = x[WindowsFnTracePrintk2-268435468]
	_ = x[WindowsFnTracePrintk3-268435469]
	_ = x[WindowsFnTracePrintk4-268435470]
	_ = x[WindowsFnTracePrintk5-268435471]
	_ = x[WindowsFnMapPushElem-268435472]
	_ = x[WindowsFnMapPopElem-268435473]
	_ = x[WindowsFnMapPeekElem-268435474]
	_ = x[WindowsFnGetCurrentPidTgid-268435475]
	_ = x[WindowsFnGetCurrentLogonId-268435476]
	_ = x[WindowsFnIsCurrentAdmin-268435477]
	_ = x[WindowsFnMemcpy-268435478]
	_ = x[WindowsFnMemcmp-268435479]
	_ = x[WindowsFnMemset-*********]
	_ = x[WindowsFnMemmove-*********]
	_ = x[WindowsFnGetSocketCookie-*********]
	_ = x[WindowsFnStrncpyS-*********]
	_ = x[WindowsFnStrncatS-*********]
	_ = x[WindowsFnStrnlenS-*********]
	_ = x[WindowsFnKtimeGetBootMs-*********]
	_ = x[WindowsFnKtimeGetMs-*********]
}

const (
	_BuiltinFunc_name_0 = "FnUnspecFnMapLookupElemFnMapUpdateElemFnMapDeleteElemFnProbeReadFnKtimeGetNsFnTracePrintkFnGetPrandomU32FnGetSmpProcessorIdFnSkbStoreBytesFnL3CsumReplaceFnL4CsumReplaceFnTailCallFnCloneRedirectFnGetCurrentPidTgidFnGetCurrentUidGidFnGetCurrentCommFnGetCgroupClassidFnSkbVlanPushFnSkbVlanPopFnSkbGetTunnelKeyFnSkbSetTunnelKeyFnPerfEventReadFnRedirectFnGetRouteRealmFnPerfEventOutputFnSkbLoadBytesFnGetStackidFnCsumDiffFnSkbGetTunnelOptFnSkbSetTunnelOptFnSkbChangeProtoFnSkbChangeTypeFnSkbUnderCgroupFnGetHashRecalcFnGetCurrentTaskFnProbeWriteUserFnCurrentTaskUnderCgroupFnSkbChangeTailFnSkbPullDataFnCsumUpdateFnSetHashInvalidFnGetNumaNodeIdFnSkbChangeHeadFnXdpAdjustHeadFnProbeReadStrFnGetSocketCookieFnGetSocketUidFnSetHashFnSetsockoptFnSkbAdjustRoomFnRedirectMapFnSkRedirectMapFnSockMapUpdateFnXdpAdjustMetaFnPerfEventReadValueFnPerfProgReadValueFnGetsockoptFnOverrideReturnFnSockOpsCbFlagsSetFnMsgRedirectMapFnMsgApplyBytesFnMsgCorkBytesFnMsgPullDataFnBindFnXdpAdjustTailFnSkbGetXfrmStateFnGetStackFnSkbLoadBytesRelativeFnFibLookupFnSockHashUpdateFnMsgRedirectHashFnSkRedirectHashFnLwtPushEncapFnLwtSeg6StoreBytesFnLwtSeg6AdjustSrhFnLwtSeg6ActionFnRcRepeatFnRcKeydownFnSkbCgroupIdFnGetCurrentCgroupIdFnGetLocalStorageFnSkSelectReuseportFnSkbAncestorCgroupIdFnSkLookupTcpFnSkLookupUdpFnSkReleaseFnMapPushElemFnMapPopElemFnMapPeekElemFnMsgPushDataFnMsgPopDataFnRcPointerRelFnSpinLockFnSpinUnlockFnSkFullsockFnTcpSockFnSkbEcnSetCeFnGetListenerSockFnSkcLookupTcpFnTcpCheckSyncookieFnSysctlGetNameFnSysctlGetCurrentValueFnSysctlGetNewValueFnSysctlSetNewValueFnStrtolFnStrtoulFnSkStorageGetFnSkStorageDeleteFnSendSignalFnTcpGenSyncookieFnSkbOutputFnProbeReadUserFnProbeReadKernelFnProbeReadUserStrFnProbeReadKernelStrFnTcpSendAckFnSendSignalThreadFnJiffies64FnReadBranchRecordsFnGetNsCurrentPidTgidFnXdpOutputFnGetNetnsCookieFnGetCurrentAncestorCgroupIdFnSkAssignFnKtimeGetBootNsFnSeqPrintfFnSeqWriteFnSkCgroupIdFnSkAncestorCgroupIdFnRingbufOutputFnRingbufReserveFnRingbufSubmitFnRingbufDiscardFnRingbufQueryFnCsumLevelFnSkcToTcp6SockFnSkcToTcpSockFnSkcToTcpTimewaitSockFnSkcToTcpRequestSockFnSkcToUdp6SockFnGetTaskStackFnLoadHdrOptFnStoreHdrOptFnReserveHdrOptFnInodeStorageGetFnInodeStorageDeleteFnDPathFnCopyFromUserFnSnprintfBtfFnSeqPrintfBtfFnSkbCgroupClassidFnRedirectNeighFnPerCpuPtrFnThisCpuPtrFnRedirectPeerFnTaskStorageGetFnTaskStorageDeleteFnGetCurrentTaskBtfFnBprmOptsSetFnKtimeGetCoarseNsFnImaInodeHashFnSockFromFileFnCheckMtuFnForEachMapElemFnSnprintfFnSysBpfFnBtfFindByNameKindFnSysCloseFnTimerInitFnTimerSetCallbackFnTimerStartFnTimerCancelFnGetFuncIpFnGetAttachCookieFnTaskPtRegsFnGetBranchSnapshotFnTraceVprintkFnSkcToUnixSockFnKallsymsLookupNameFnFindVmaFnLoopFnStrncmpFnGetFuncArgFnGetFuncRetFnGetFuncArgCntFnGetRetvalFnSetRetvalFnXdpGetBuffLenFnXdpLoadBytesFnXdpStoreBytesFnCopyFromUserTaskFnSkbSetTstampFnImaFileHashFnKptrXchgFnMapLookupPercpuElemFnSkcToMptcpSockFnDynptrFromMemFnRingbufReserveDynptrFnRingbufSubmitDynptrFnRingbufDiscardDynptrFnDynptrReadFnDynptrWriteFnDynptrDataFnTcpRawGenSyncookieIpv4FnTcpRawGenSyncookieIpv6FnTcpRawCheckSyncookieIpv4FnTcpRawCheckSyncookieIpv6FnKtimeGetTaiNsFnUserRingbufDrainFnCgrpStorageGetFnCgrpStorageDelete"
	_BuiltinFunc_name_1 = "WindowsFnMapLookupElemWindowsFnMapUpdateElemWindowsFnMapDeleteElemWindowsFnMapLookupAndDeleteElemWindowsFnTailCallWindowsFnGetPrandomU32WindowsFnKtimeGetBootNsWindowsFnGetSmpProcessorIdWindowsFnKtimeGetNsWindowsFnCsumDiffWindowsFnRingbufOutputWindowsFnTracePrintk2WindowsFnTracePrintk3WindowsFnTracePrintk4WindowsFnTracePrintk5WindowsFnMapPushElemWindowsFnMapPopElemWindowsFnMapPeekElemWindowsFnGetCurrentPidTgidWindowsFnGetCurrentLogonIdWindowsFnIsCurrentAdminWindowsFnMemcpyWindowsFnMemcmpWindowsFnMemsetWindowsFnMemmoveWindowsFnGetSocketCookieWindowsFnStrncpySWindowsFnStrncatSWindowsFnStrnlenSWindowsFnKtimeGetBootMsWindowsFnKtimeGetMs"
)

var (
	_BuiltinFunc_index_0 = [...]uint16{0, 8, 23, 38, 53, 64, 76, 89, 104, 123, 138, 153, 168, 178, 193, 212, 230, 246, 264, 277, 289, 306, 323, 338, 348, 363, 380, 394, 406, 416, 433, 450, 466, 481, 497, 512, 528, 544, 568, 583, 596, 608, 624, 639, 654, 669, 683, 700, 714, 723, 735, 750, 763, 778, 793, 808, 828, 847, 859, 875, 894, 910, 925, 939, 952, 958, 973, 990, 1000, 1022, 1033, 1049, 1066, 1082, 1096, 1115, 1133, 1148, 1158, 1169, 1182, 1202, 1219, 1238, 1259, 1272, 1285, 1296, 1309, 1321, 1334, 1347, 1359, 1373, 1383, 1395, 1407, 1416, 1429, 1446, 1460, 1479, 1494, 1517, 1536, 1555, 1563, 1572, 1586, 1603, 1615, 1632, 1643, 1658, 1675, 1693, 1713, 1725, 1743, 1754, 1773, 1794, 1805, 1821, 1849, 1859, 1875, 1886, 1896, 1908, 1928, 1943, 1959, 1974, 1990, 2004, 2015, 2030, 2044, 2066, 2087, 2102, 2116, 2128, 2141, 2156, 2173, 2193, 2200, 2214, 2227, 2241, 2259, 2274, 2285, 2297, 2311, 2327, 2346, 2365, 2378, 2396, 2410, 2424, 2434, 2450, 2460, 2468, 2487, 2497, 2508, 2526, 2538, 2551, 2562, 2579, 2591, 2610, 2624, 2639, 2659, 2668, 2674, 2683, 2695, 2707, 2722, 2733, 2744, 2759, 2773, 2788, 2806, 2820, 2833, 2843, 2864, 2880, 2895, 2917, 2938, 2960, 2972, 2985, 2997, 3021, 3045, 3071, 3097, 3112, 3130, 3146, 3165}
	_BuiltinFunc_index_1 = [...]uint16{0, 22, 44, 66, 97, 114, 136, 159, 185, 204, 221, 243, 264, 285, 306, 327, 347, 366, 386, 412, 438, 461, 476, 491, 506, 522, 546, 563, 580, 597, 620, 639}
)

func (i BuiltinFunc) String() string {
	switch {
	case i <= 211:
		return _BuiltinFunc_name_0[_BuiltinFunc_index_0[i]:_BuiltinFunc_index_0[i+1]]
	case 268435457 <= i && i <= *********:
		i -= 268435457
		return _BuiltinFunc_name_1[_BuiltinFunc_index_1[i]:_BuiltinFunc_index_1[i+1]]
	default:
		return "BuiltinFunc(" + strconv.FormatInt(int64(i), 10) + ")"
	}
}
