// Code generated by "stringer -linecomment -output=btf_types_string.go -type=FuncLinkage,VarLinkage,btfKind"; DO NOT EDIT.

package btf

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[StaticFunc-0]
	_ = x[GlobalFunc-1]
	_ = x[ExternFunc-2]
}

const _FuncLinkage_name = "staticglobalextern"

var _FuncLinkage_index = [...]uint8{0, 6, 12, 18}

func (i FuncLinkage) String() string {
	if i < 0 || i >= FuncLinkage(len(_FuncLinkage_index)-1) {
		return "FuncLinkage(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _FuncLinkage_name[_FuncLinkage_index[i]:_FuncLinkage_index[i+1]]
}
func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[StaticVar-0]
	_ = x[GlobalVar-1]
	_ = x[ExternVar-2]
}

const _VarLinkage_name = "staticglobalextern"

var _VarLinkage_index = [...]uint8{0, 6, 12, 18}

func (i VarLinkage) String() string {
	if i < 0 || i >= VarLinkage(len(_VarLinkage_index)-1) {
		return "VarLinkage(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _VarLinkage_name[_VarLinkage_index[i]:_VarLinkage_index[i+1]]
}
func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[kindUnknown-0]
	_ = x[kindInt-1]
	_ = x[kindPointer-2]
	_ = x[kindArray-3]
	_ = x[kindStruct-4]
	_ = x[kindUnion-5]
	_ = x[kindEnum-6]
	_ = x[kindForward-7]
	_ = x[kindTypedef-8]
	_ = x[kindVolatile-9]
	_ = x[kindConst-10]
	_ = x[kindRestrict-11]
	_ = x[kindFunc-12]
	_ = x[kindFuncProto-13]
	_ = x[kindVar-14]
	_ = x[kindDatasec-15]
	_ = x[kindFloat-16]
	_ = x[kindDeclTag-17]
	_ = x[kindTypeTag-18]
	_ = x[kindEnum64-19]
}

const _btfKind_name = "UnknownIntPointerArrayStructUnionEnumForwardTypedefVolatileConstRestrictFuncFuncProtoVarDatasecFloatDeclTagTypeTagEnum64"

var _btfKind_index = [...]uint8{0, 7, 10, 17, 22, 28, 33, 37, 44, 51, 59, 64, 72, 76, 85, 88, 95, 100, 107, 114, 120}

func (i btfKind) String() string {
	if i >= btfKind(len(_btfKind_index)-1) {
		return "btfKind(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _btfKind_name[_btfKind_index[i]:_btfKind_index[i+1]]
}
