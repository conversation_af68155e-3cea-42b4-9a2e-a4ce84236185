on:
  workflow_run:
    workflows: ["apidiff"]
    types:
      - completed

permissions:
  pull-requests: write

jobs:
  tag-breaking-change:
    name: Tag breaking changes
    runs-on: ubuntu-latest
    if: github.event.workflow_run.event == 'pull_request'
    steps:
      - name: 'Download artifact'
        uses: actions/github-script@v7
        with:
          script: |
            var artifacts = await github.rest.actions.listWorkflowRunArtifacts({
               owner: context.repo.owner,
               repo: context.repo.repo,
               run_id: ${{github.event.workflow_run.id }},
            });
            var matchArtifact = artifacts.data.artifacts.filter((artifact) => {
              return artifact.name == "apidiff"
            })[0];
            var download = await github.rest.actions.downloadArtifact({
               owner: context.repo.owner,
               repo: context.repo.repo,
               artifact_id: matchArtifact.id,
               archive_format: 'zip',
            });
            var fs = require('fs');
            fs.writeFileSync('${{github.workspace}}/apidiff.zip', Buffer.from(download.data));
      - run: unzip apidiff.zip
      - name: 'Add or remove label'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            var fs = require('fs');
            var jsonData = JSON.parse(fs.readFileSync('apidiff.json', 'utf8'));

            var issueNumber = jsonData.id;
            var semverType = jsonData["semver-type"];

            if (semverType === 'major') {
              // Add 'breaking-change' label
              await github.rest.issues.addLabels({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: issueNumber,
                labels: ['breaking-change']
              });
            } else {
              // Remove 'breaking-change' label if it exists
              try {
                await github.rest.issues.removeLabel({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: issueNumber,
                  name: 'breaking-change'
                });
              } catch (error) {
                console.log('Label breaking-change not found or already removed');
              }
            }