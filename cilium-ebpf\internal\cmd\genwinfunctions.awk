#!/usr/bin/gawk -f
# Generate constants from eBPF for Windows headers.
#
# This script expects include/ebpf_structs.h as input.

BEGIN {
	print "// Code generated by internal/cmd/genwinfunctions.awk; DO NOT EDIT."
	print ""
	print "package asm"
	print ""
	print "// Code in this file is derived from eBPF for Windows, available under the MIT License."
	print ""
	print "import \"github.com/cilium/ebpf/internal/platform\""
	print ""
	print "// Built-in functions (Windows)."
	print "const ("
}

/BPF_FUNC_[[:alnum:]_]+ *= *[0-9]+,/ {
	name = gensub(/.*BPF_FUNC_([[:alnum:]_]+) *=.*/, "\\1", 1)
	id = gensub(/.*BPF_FUNC_[[:alnum:]_]+ *= *([0-9]+),.*/, "\\1", 1)

	split(tolower(name), parts, "_")
	result = "WindowsFn"
	for (i in parts) {
		part = parts[i]
		result = result substr(toupper(substr(part,1,1)), 1, 1) substr(part, 2)
	}

	print "\t" result " = BuiltinFunc(platform.WindowsTag | " id ")"
}

END {
	print ")"
	print ""
}
