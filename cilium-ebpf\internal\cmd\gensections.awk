#!/usr/bin/gawk -f
# This script expects tools/lib/bpf/libbpf.c as input.

function trim(str, left, right) {
	str = gensub("^[\t ]*" left, "", "g", str)
	return gensub(right "$", "", "g", str)
}

BEGIN {
	print "// Code generated by internal/cmd/gensections.awk; DO NOT EDIT."
	print ""
	print "package ebpf"
	print ""
	print "// Code in this file is derived from libbpf, available under BSD-2-Clause."
	print ""
	print "import \"github.com/cilium/ebpf/internal/sys\""
	print ""
	print "var elfSectionDefs = []libbpfElfSectionDef{"

	FS=","
}

/\tSEC_DEF/ {
	pattern = trim(substr($1, 10))
	prog_type = "sys.BPF_PROG_TYPE_" trim($2)
	attach_type = trim($3)
	attach_type = attach_type == "0" ? "0" : "sys." attach_type
	flags = trim($4, "", ")")
	flags = gensub("SEC_", "_SEC_", "g", flags)
	printf "\t{%s, %s, %s, %s},\n", pattern, prog_type, attach_type, flags;
}

END {
	print "}"
	print ""
}
