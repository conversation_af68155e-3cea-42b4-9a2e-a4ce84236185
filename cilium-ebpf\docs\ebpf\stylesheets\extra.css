/* Tagline on landing page. */
.tagline {
  font-size: 3em;
  font-weight: 900;
  letter-spacing: -0.5px;
  background: linear-gradient(120deg, #4051B5, 35%, #6AD6E4);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* :progress-wrench:
   Custom 'incomplete' admonition for sections that need work or maintenance.
   Create blocks using '!!! incomplete'.
*/
:root {
  --md-admonition-icon--incomplete: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13 2.03v2.02c4.39.54 7.5 4.53 6.96 8.92-.46 3.64-3.32 6.53-6.96 6.96v2c5.5-.55 9.5-5.43 8.95-10.93-.45-4.75-4.22-8.5-8.95-8.97m-2 .03c-1.95.19-3.81.94-5.33 2.2L7.1 5.74c1.12-.9 2.47-1.48 3.9-1.68v-2M4.26 5.67A9.885 9.885 0 0 0 2.05 11h2c.19-1.42.75-2.77 1.64-3.9L4.26 5.67M2.06 13c.2 1.96.97 3.81 2.21 5.33l1.42-1.43A8.002 8.002 0 0 1 4.06 13h-2m5.04 5.37-1.43 1.37A9.994 9.994 0 0 0 11 22v-2a8.002 8.002 0 0 1-3.9-1.63m9.72-3.18-4.11-4.11c.41-1.04.18-2.26-.68-3.11-.9-.91-2.25-1.09-3.34-.59l1.94 1.94-1.35 1.36-1.99-1.95c-.54 1.09-.29 2.44.59 ********** 2.08 1.08 3.12.68l4.11 4.1c.***********.63 0l1.04-1.03c.22-.18.22-.5.04-.64Z"/></svg>')
}

.md-typeset .admonition.incomplete,
.md-typeset details.incomplete {
  border-color: rgb(255, 204, 77);
}

.md-typeset .incomplete>.admonition-title,
.md-typeset .incomplete>summary {
  background-color: rgba(255, 204, 77, 0.1);
}

.md-typeset .incomplete>.admonition-title::before,
.md-typeset .incomplete>summary::before {
  background-color: rgb(255, 204, 77);
  -webkit-mask-image: var(--md-admonition-icon--incomplete);
  mask-image: var(--md-admonition-icon--incomplete);
}

/* gp and go are the classes used for prompt and output in shell-session code
   blocks. Prevent these from being highlighted as it hurts UX.
*/
.highlight .gp,
.highlight .go {
  user-select: none;
}

.md-typeset {
  .md-badge {
    font-size: 0.85em;

    .md-badge__icon {
      padding: 0.4em;
      background: var(--md-accent-fg-color--transparent);
      border-start-start-radius: 0.1em;
      border-end-start-radius: 0.1em;
    }

    .md-badge__text {
      padding: 0.4em 0.8em;
      border-start-end-radius: 0.1em;
      border-end-end-radius: 0.1em;
      box-shadow: 0 0 0 1px inset var(--md-accent-fg-color--transparent);
    }
  }

  .md-badge--right {
    float: right;
    margin-left: 0.35em;
  }
}
