#!/usr/bin/gawk -f
# Generate constants from Linux headers.
#
# This script expects include/uapi/bpf.h as input.

BEGIN {
	print "// Code generated by internal/cmd/genfunctions.awk; DO NOT EDIT."
	print ""
	print "package asm"
	print ""
	print "// Code in this file is derived from Linux, available under the GPL-2.0 WITH Linux-syscall-note."
	print ""
	print "import \"github.com/cilium/ebpf/internal/platform\""
	print ""
	print "// Built-in functions (Linux)."
	print "const ("
}

/FN\([[:alnum:]_]+, [[:digit:]]+,.*\)/ {
	name = gensub(/.*FN\(([[:alnum:]_]+), [[:digit:]]+,.*\).*/, "\\1", 1)
	id = gensub(/.*FN\([[:alnum:]_]+, ([[:digit:]]+),.*\).*/, "\\1", 1)

	split(tolower(name), parts, "_")
	result = "Fn"
	for (i in parts) {
		part = parts[i]
		result = result substr(toupper(substr(part,1,1)), 1, 1) substr(part, 2)
	}

	print "\t" result " = BuiltinFunc(platform.LinuxTag | " id ")"
}

END {
	print ")"
	print ""
}
