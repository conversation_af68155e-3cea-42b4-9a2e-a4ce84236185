name: ci
on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

env:
  TMPDIR: /tmp
  CI_MAX_KERNEL_VERSION: '6.11'
  CI_MAX_EFW_VERSION: '0.21.0'
  CI_MIN_CLANG_VERSION: '13'
  go_version: '~1.24'
  prev_go_version: '~1.23'
  CGO_ENABLED: '0'
  # Sync with Pipfile and netlify.toml.
  python_version: '~3.13'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.event_name == 'pull_request' }}

jobs:
  build-and-lint:
    name: Build and Lint
    runs-on: ubuntu-22.04
    timeout-minutes: 10
    steps:
      - uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '${{ env.go_version }}'

      - name: Run staticcheck
        uses: dominikh/staticcheck-action@v1
        with:
          version: "latest"
          install-go: false

      - name: Run golangci-lint
        uses: golangci/golangci-lint-action@v8.0.0

      - name: Generate and format code
        run: |
          make clean && make container-all
          if ! git diff --exit-code; then
            echo "found unformatted source files, or generated files are not up to date, run 'make'" >&2
            exit 1
          fi

      - name: Test bpf2go
        run: |
          go test -v ./cmd/bpf2go

      - name: Build
        run: go build -v ./...

  cross-build:
    name: Cross build
    runs-on: ubuntu-22.04
    needs: build-and-lint
    timeout-minutes: 10
    steps:
      - uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '${{ env.go_version }}'

      - name: Cross build darwin
        env:
          GOOS: darwin
        run: |
          go build -v ./...
          go test -c -o /dev/null ./... >/dev/null

      - name: Cross build arm32
        env:
          GOARCH: arm
          GOARM: 6
        run: |
          go build -v ./...
          go test -c -o /dev/null ./... >/dev/null

      - name: Cross build wasm
        env:
          GOOS: js
          GOARCH: wasm
        run: |
          go build -v ./...
          go test -c -o /dev/null ./... >/dev/null

  build-docs:
    name: Build Documentation
    runs-on: ubuntu-22.04
    timeout-minutes: 10
    steps:
      - uses: actions/checkout@v4
        with:
          # The mkdocs git-authors plugin needs access to the full revision
          # history to correctly generate its statistics.
          fetch-depth: 0

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '${{ env.go_version }}'

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '${{ env.python_version }}'
          cache: 'pipenv'

      - name: Install pipenv
        run: pip3 install pipenv

      - name: Install Dependencies
        run: pipenv install
        working-directory: ./docs

      - name: Build Documentation
        run: make build
        working-directory: ./docs

  test-on-prev-go:
    name: Run tests on previous stable Go
    runs-on: ubuntu-latest
    needs: build-and-lint
    timeout-minutes: 15
    env:
      CI_KERNEL_SELFTESTS: '/usr/src/linux/tools/testing/selftests/bpf'
    steps:
      - uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '${{ env.prev_go_version }}'

      - run: go install lmb.io/vimto@latest
      - run: go install gotest.tools/gotestsum@v1.12.0
      - run: sudo apt-get update && sudo apt-get install -y --no-install-recommends qemu-system-x86
      - run: sudo chmod 0666 /dev/kvm

      - name: Test
        env:
          GOTRACEBACK: crash
          CGO_ENABLED: 1 # CGo is required by `-race`
        run: |
          gotestsum --raw-command --ignore-non-json-output-lines --junitfile junit.xml -- vimto -kernel :stable-selftests -- go test -race -timeout 5m -short -count 1 -json ./...

      - name: Benchmark
        run: vimto -kernel :stable-selftests -- go test -short -run '^$' -bench . -benchtime=1x ./...

      - name: Upload coredumps
        uses: actions/upload-artifact@v4
        if: ${{ failure() }}
        with:
          name: cores
          if-no-files-found: ignore
          path: |
            **/core-*
            **/*.test

      - name: Upload Test Results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: Test Results (previous stable Go)
          path: junit.xml

  test-on-arm64:
    name: Run tests on arm64
    runs-on: ubuntu-24.04-arm64
    needs: build-and-lint
    timeout-minutes: 15
    env:
      EBPF_TEST_IGNORE_VERSION: 'TestKprobeMulti,TestKprobeMultiErrors,TestKprobeMultiCookie,TestKprobeMultiProgramCall,TestHaveBPFLinkKprobeMulti,TestKprobeSession,TestHaveBPFLinkKprobeSession,TestHaveProgramType/LircMode2'
    steps:
      - uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '${{ env.go_version }}'

      - run: go install gotest.tools/gotestsum@v1.12.0

      - name: Test
        # Skip TestGoarches/loong64 because the GH arm64 Go toolchain seems to be weird.
        run: gotestsum --ignore-non-json-output-lines --junitfile junit.xml -- -exec 'sudo -E' -short -count 1 -skip '^TestGoarches/loong64$' -json ./...

      - name: Benchmark
        run: go test -exec sudo -short -run '^$' -bench . -benchtime=1x ./...

      - name: Upload Test Results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: Test Results (arm64)
          path: junit.xml

      - name: Show dmesg
        if: failure()
        run: |
          sudo dmesg

  linux-test:
    name: Run tests (Linux)
    runs-on: ubuntu-latest
    needs: build-and-lint
    timeout-minutes: 15
    strategy:
      matrix:
        tag:
          - "mainline"
          - "stable"
          - "6.6"
          - "6.1"
          - "5.15"
          - "5.10"
          - "5.4"
          - "4.19"
          - "4.14"
    steps:
      - uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '${{ env.go_version }}'

      - run: go install gotest.tools/gotestsum@v1.12.0
      - run: go install lmb.io/vimto@latest
      - run: sudo apt-get update && sudo apt-get install -y --no-install-recommends qemu-system-x86
      - run: sudo chmod 0666 /dev/kvm

      - name: Test
        run: gotestsum --raw-command --ignore-non-json-output-lines --junitfile junit.xml -- vimto -kernel :${{ matrix.tag }} -- go test -short -count 1 -json ./...

      - name: Upload Test Results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: Test Results (linux ${{ matrix.tag }})
          path: junit.xml

  windows-test:
    name: Run tests (Windows)
    runs-on: windows-2022
    needs: build-and-lint
    timeout-minutes: 15
    strategy:
      matrix:
        version:
          - "main"
    env:
      # Fix slow Go compile and cache restore
      # See https://github.com/actions/setup-go/pull/515
      GOCACHE: D:\gocache
      GOMODCACHE: D:\gomodcache
      # Avoid putting temp on slow C:
      TEMP: D:\temp
      CI_EFW_VERSION: "0.21.0"

    steps:
      - run: mkdir D:\temp
        shell: pwsh

      - name: Get eBPF for Windows download URL
        id: determine-url
        uses: actions/github-script@v7
        with:
          script: |
            if ("${{ matrix.version }}" != "main") {
              // TODO: Workflow artifact and release artifact don't have the
              // same folder structure.
              core.setFailed('Installing tagged versions is not supported');
              return;
            }

            // Get the latest successful merge_group run
            const workflow_runs = await github.rest.actions.listWorkflowRuns({
              owner: 'microsoft',
              repo: 'ebpf-for-windows',
              workflow_id: 'cicd.yml',
              event: 'schedule',
              branch: 'main',
              status: 'completed',
              per_page: 1
            });

            if (workflow_runs.data.workflow_runs.length === 0) {
              core.setFailed('No successful merge_group workflow runs found');
              return;
            }

            // Get artifacts from this run
            const run_id = workflow_runs.data.workflow_runs[0].id;
            const run_url = workflow_runs.data.workflow_runs[0].html_url;
            console.log(`Using workflow run: ${run_url}`);

            const artifacts = await github.rest.actions.listWorkflowRunArtifacts({
              owner: 'microsoft',
              repo: 'ebpf-for-windows',
              run_id: run_id
            });

            // Find the specific artifact
            const artifact = artifacts.data.artifacts.find(a => a.name === 'Build-x64-Debug');

            if (!artifact) {
              console.log('Available artifacts:', artifacts.data.artifacts.map(a => a.name));
              core.setFailed('Build-x64-Debug artifact not found in the workflow run');
              return;
            }

            // Get the download URL via redirect
            const response = await github.rest.actions.downloadArtifact({
              owner: 'microsoft',
              repo: 'ebpf-for-windows',
              artifact_id: artifact.id,
              archive_format: 'zip',
              request: {
                redirect: 'manual'
              }
            });

            // Extract the location header which contains the actual download URL
            const download_url = response.url;

            if (!download_url) {
              core.setFailed('Failed to get redirect URL from headers');
              return;
            }

            core.setOutput('download_url', download_url);

      - name: Download and Install eBPF for Windows
        shell: pwsh
        run: |
          Invoke-WebRequest -Uri "${{ steps.determine-url.outputs.download_url }}" -OutFile "$env:TEMP\efw.zip"
          Expand-Archive -Path "$env:TEMP\efw.zip" -DestinationPath "$env:TEMP"
          Expand-Archive -Path "$env:TEMP\build-Debug.zip" -DestinationPath "$env:TEMP\ebpf"
          $setupScript = Get-ChildItem -Path "$env:TEMP\ebpf" -Filter "setup-ebpf.ps1" -Recurse | Select-Object -First 1
          if ($setupScript) {
            Write-Host "Found setup script: $($setupScript.FullName)"
            Set-Location -Path $setupScript.DirectoryName
            Write-Host "Changed directory to: $(Get-Location)"
            & $setupScript.FullName
          } else {
            Write-Error "Setup script not found in the extracted package"
            exit 1
          }

      - name: Add eBPF for Windows to PATH
        shell: pwsh
        run: echo "C:\Program Files\ebpf-for-windows\" | Out-File -FilePath $env:GITHUB_PATH -Encoding utf8 -Append

      - uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '${{ env.go_version }}'

      - run: go install gotest.tools/gotestsum@v1.12.0

      - name: Test
        run: >
          gotestsum --raw-command --ignore-non-json-output-lines --junitfile junit.xml --
          go test -short -count 1 -json ./...

      - name: Upload Test Results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: Test Results (windows ${{ matrix.tag }})
          path: junit.xml

