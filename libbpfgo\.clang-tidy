Checks: ',
  -*,
  abseil-duration-addition,
  abseil-duration-comparison,
  abseil-duration-conversion-cast,
  abseil-duration-division,
  abseil-duration-factory-float,
  abseil-duration-factory-scale,
  abseil-duration-subtraction,
  abseil-duration-unnecessary-conversion,
  abseil-faster-strsplit-delimiter,
  abseil-no-internal-dependencies,
  abseil-no-namespace,
  abseil-redundant-strcat-calls,
  abseil-str-cat-append,
  abseil-string-find-startswith,
  abseil-string-find-str-contains,
  abseil-time-comparison,
  abseil-time-subtraction,
  abseil-upgrade-duration-conversions,
  ,
  boost-use-to-string,
  ,
  bugprone-argument-comment,
  bugprone-assert-side-effect,
  bugprone-bad-signal-to-kill-thread,
  bugprone-bool-pointer-implicit-conversion,
  -bugprone-branch-clone,
  bugprone-copy-constructor-init,
  bugprone-dangling-handle,
  bugprone-dynamic-static-initializers,
  bugprone-exception-escape,
  bugprone-fold-init-type,
  bugprone-forward-declaration-namespace,
  bugprone-forwarding-reference-overload,
  -bugprone-implicit-widening-of-multiplication-result, # TODO: check
  bugprone-inaccurate-erase,
  bugprone-incorrect-roundings,
  bugprone-infinite-loop,
  bugprone-integer-division,
  bugprone-lambda-function-name,
  bugprone-macro-repeated-side-effects,
  bugprone-misplaced-operator-in-strlen-in-alloc,
  bugprone-misplaced-pointer-arithmetic-in-alloc,
  bugprone-misplaced-widening-cast,
  bugprone-move-forwarding-reference,
  bugprone-multiple-statement-macro,
  bugprone-no-escape,
  bugprone-not-null-terminated-result,
  bugprone-parent-virtual-call,
  bugprone-posix-return,
  bugprone-redundant-branch-condition,
  bugprone-signal-handler,
  bugprone-signed-char-misuse,
  bugprone-sizeof-container,
  bugprone-spuriously-wake-up-functions,
  bugprone-string-constructor,
  bugprone-string-integer-assignment,
  bugprone-string-literal-with-embedded-nul,
  bugprone-suspicious-enum-usage,
  bugprone-suspicious-include,
  bugprone-suspicious-memset-usage,
  bugprone-suspicious-missing-comma,
  bugprone-suspicious-semicolon,
  bugprone-suspicious-string-compare,
  bugprone-swapped-arguments,
  bugprone-terminating-continue,
  bugprone-throw-keyword-missing,
  bugprone-too-small-loop-variable,
  bugprone-undefined-memory-manipulation,
  bugprone-undelegated-constructor,
  bugprone-unhandled-exception-at-new,
  bugprone-unhandled-self-assignment,
  bugprone-unused-raii,
  bugprone-unused-return-value,
  bugprone-use-after-move,
  bugprone-virtual-near-miss,
  ,
  cert-con36-c,
  cert-env33-c,
  cert-err34-c,
  cert-fio38-c,
  cert-flp30-c,
  cert-msc30-c,
  cert-msc32-c,
  cert-pos44-c,
  cert-pos47-c,
  cert-sig30-c,
  cert-str34-c,
  ,
  fuchsia-default-arguments-calls,
  fuchsia-default-arguments-declarations,
  fuchsia-overloaded-operator,
  fuchsia-trailing-return,
  fuchsia-virtual-inheritance,
  ,
  hicpp-avoid-c-arrays,
  hicpp-avoid-goto,
  -hicpp-deprecated-headers,
  hicpp-exception-baseclass,
  hicpp-explicit-conversions,
  hicpp-function-size,
  hicpp-invalid-access-moved,
  hicpp-member-init,
  hicpp-move-const-arg,
  -hicpp-multiway-paths-covered,
  hicpp-named-parameter,
  hicpp-no-array-decay,
  -hicpp-no-assembler,
  -hicpp-no-malloc,
  hicpp-noexcept-move,
  hicpp-static-assert,
  hicpp-uppercase-literal-suffix,
  hicpp-use-auto,
  hicpp-use-emplace,
  hicpp-use-equals-default,
  hicpp-use-equals-delete,
  hicpp-use-noexcept,
  hicpp-use-nullptr,
  hicpp-use-override,
  hicpp-vararg,
  ,
  readability-avoid-const-params-in-decls,
  -readability-braces-around-statements,
  -readability-const-return-type,
  readability-container-size-empty,
  readability-convert-member-functions-to-static,
  readability-delete-null-pointer,
  -readability-else-after-return,
  -readability-function-cognitive-complexity,
  readability-function-size,
  readability-identifier-naming,
  readability-implicit-bool-conversion,
  readability-inconsistent-declaration-parameter-name,
  -readability-isolate-declaration,
  -readability-magic-numbers,
  readability-make-member-function-const,
  readability-misleading-indentation,
  readability-misplaced-array-index,
  readability-named-parameter,
  readability-non-const-parameter,
  readability-qualified-auto,
  readability-redundant-access-specifiers,
  readability-redundant-control-flow,
  readability-redundant-declaration,
  readability-redundant-function-ptr-dereference,
  readability-redundant-member-init,
  readability-redundant-preprocessor,
  readability-redundant-smartptr-get,
  readability-redundant-string-cstr,
  readability-redundant-string-init,
  readability-simplify-boolean-expr,
  readability-simplify-subscript-expr,
  readability-static-accessed-through-instance,
  readability-static-definition-in-anonymous-namespace,
  readability-string-compare,
  readability-suspicious-call-argument,
  readability-uniqueptr-delete-release,
  readability-uppercase-literal-suffix,
  readability-use-anyofallof,
  ,
  '
FormatStyle: 'file'
