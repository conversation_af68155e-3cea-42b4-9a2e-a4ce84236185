[36m┌─ [17:39:29.484915] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb14480e1[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffff9c[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xc0000ee2c0[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0xffff9f360a23bdb0[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x88000[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x101[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffff9c (user space)[0m
[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xc0000ee2c0 (user space)[0m
[1m├─ Stack ID: [0m[36m617[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.490524] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.492156] [0m[34mksoftirqd/4:27/27[0m[2m on CPU 4[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffffffffb29ab536[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2fc1179c00[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x100[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xffffffffb29ab536 (kernel space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.492465] [0m[34mkworker/4:0:16770/16770[0m[2m on CPU 4[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffff8c3078d32b00[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2fe411c9c0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c2fe411c9c0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.496042] [0m[34mswapper/6:0/0[0m[2m on CPU 6[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f80350000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffffffffb11e2ca0[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10091392c[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x20[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c3078da2128[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xffffffffb11e2ca0 -> process_timeout (kernel)[0m
[2m│  [0m[2m├─ Ptr[2]: [0m[37m0x10091392c (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.496075] [0m[34mrcu_preempt:17/17[0m[2m on CPU 6[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x246[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffffffffb33a6ce0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c3078da2100[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[2m│  [0m[2m├─ Ptr[4]: [0m[37m0xffffffffb33a6ce0 (kernel space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.500590] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59873d076[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59873d076 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.500680] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.500717] [0m[34mtracer:18251/18254[0m[2m on CPU 4[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffff8c307306a208[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c307306a208[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c307306a208[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m894[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.500768] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59876a2ae[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59876a2ae (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.500787] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c3073069108[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c3073069108[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m12[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.502305] [0m[34mswapper/5:0/0[0m[2m on CPU 5[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f8295c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2f8295c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e5988e05b0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e5988e05b0 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.502364] [0m[34mchronyd:209/209[0m[2m on CPU 5[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x80[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m38[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.503823] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.503851] [0m[34mtracer:18251/18254[0m[2m on CPU 4[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffff8c307306a208[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c307306a208[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c307306a208[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m894[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.503907] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598a6858b[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598a6858b (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.503924] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.504026] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10102[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598a85a6b[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598a85a6b (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.504043] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.504079] [0m[34mswapper/6:0/0[0m[2m on CPU 6[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f80350000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffffffffb11e2ca0[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10091392e[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x20[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c3078da2128[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xffffffffb11e2ca0 -> process_timeout (kernel)[0m
[2m│  [0m[2m├─ Ptr[2]: [0m[37m0x10091392e (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.504101] [0m[34mrcu_preempt:17/17[0m[2m on CPU 6[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x246[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffffffffb33a6ce0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c3078da2100[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[2m│  [0m[2m├─ Ptr[4]: [0m[37m0xffffffffb33a6ce0 (kernel space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.504130] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598a9edb2[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598a9edb2 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.504144] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.504230] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598ab78dc[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598ab78dc (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.504244] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.504332] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598ad069b[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598ad069b (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.504347] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.504433] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x16[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598ae9006[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598ae9006 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.504447] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.504534] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598b0183f[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598b0183f (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.504547] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.504633] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598b19b72[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598b19b72 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.504660] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.504746] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598b35436[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598b35436 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.504759] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.504844] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598b4d3a2[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598b4d3a2 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.504857] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.504941] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598b650e5[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598b650e5 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.504955] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.505038] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598b7cc83[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598b7cc83 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.505052] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.505136] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598b94a14[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598b94a14 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.505150] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.505233] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598bac4ee[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598bac4ee (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.505247] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.505331] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598bc44c3[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598bc44c3 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.505345] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.505431] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598bdc91a[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598bdc91a (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.505446] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.505531] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598bf5070[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598bf5070 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.505547] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.505633] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598c0ddf3[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598c0ddf3 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.505649] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.505744] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598c28d2a[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598c28d2a (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.505759] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.505858] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598c43fb1[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598c43fb1 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.505884] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.506004] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598c67073[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598c67073 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.506041] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.506150] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x16[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598c8b768[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598c8b768 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.506174] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.506273] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598ca9c43[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598ca9c43 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.506296] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.506399] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598cc8949[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598cc8949 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.506423] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.506514] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598ce49ba[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598ce49ba (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.506531] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.506622] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598cff2e8[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598cff2e8 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.506639] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.506732] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598d19f00[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598d19f00 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.506751] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.506889] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598d3eda5[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598d3eda5 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.506933] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.507060] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598d699c1[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598d699c1 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.507088] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.507184] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598d8871a[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598d8871a (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.507201] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.507291] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598da27da[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598da27da (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.507307] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.507397] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598dbc665[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598dbc665 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.507413] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.507526] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598ddafb7[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598ddafb7 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.507554] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.507646] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598df944a[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598df944a (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.507661] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.507748] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598e122d6[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598e122d6 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.507761] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.507851] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598e2b143[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598e2b143 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.507867] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.507958] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598e453a2[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598e453a2 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.507973] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.508063] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x12[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10102[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598e5ec34[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598e5ec34 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.508081] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.508177] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598e7aa6a[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598e7aa6a (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.508196] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.508292] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598e96980[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598e96980 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.508313] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.508407] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598eb2d3b[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598eb2d3b (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.508429] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.508562] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598ed8868[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598ed8868 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.508583] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.508668] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598ef26cf[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598ef26cf (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.508686] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.508795] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598f0c51f[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598f0c51f (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.508814] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.508919] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598f2f080[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598f2f080 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.508950] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.509051] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598f4ff6a[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598f4ff6a (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.509072] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.509162] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598f6b769[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598f6b769 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.509178] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.509282] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598f87a89[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598f87a89 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.509320] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.509410] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598fa818f[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598fa818f (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.509425] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.509532] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598fc50c3[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598fc50c3 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.509558] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.509654] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e598fe34b5[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e598fe34b5 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.509672] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.509797] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e5990060dc[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e5990060dc (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.509820] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.509968] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599030102[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599030102 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.509982] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.510203] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x12[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e5990698a6[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e5990698a6 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.510217] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.510710] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e5990e4084[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e5990e4084 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.510749] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.511500] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e5991a5bb7[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e5991a5bb7 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.511522] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.512035] [0m[34mswapper/6:0/0[0m[2m on CPU 6[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f80350000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffffffffb11e2ca0[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x100913930[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x20[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c3078da2128[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xffffffffb11e2ca0 -> process_timeout (kernel)[0m
[2m│  [0m[2m├─ Ptr[2]: [0m[37m0x100913930 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.512057] [0m[34mrcu_preempt:17/17[0m[2m on CPU 6[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x246[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffffffffb33a6ce0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c3078da2100[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[2m│  [0m[2m├─ Ptr[4]: [0m[37m0xffffffffb33a6ce0 (kernel space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.512814] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e5992e689f[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e5992e689f (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.512834] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.515469] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59956da13[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59956da13 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.515502] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.515817] [0m[34mswapper/2:0/0[0m[2m on CPU 2[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2fbdbd4000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2fbdbd4000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e5995c32b2[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e5995c32b2 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.515864] [0m[34mredis-server:947/947[0m[2m on CPU 2[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb14480e1[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffff9c[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x644156245e97[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0xffff9f3603db3c18[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x8000[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x101[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x644156245e97[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffff9c (user space)[0m
[2m│  [0m[2m├─ Ptr[1]: [0m[37m0x644156245e97 (user space)[0m
[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x644156245e97 (user space)[0m
[1m├─ Stack ID: [0m[36m17[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.515906] [0m[34mredis-server:947/947[0m[2m on CPU 2[0m[31m [exit][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1448739[0m[2m (__builtin__ftrace)[0m
[1m├─ Duration: [0m[32m43.586µs[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffff9c[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x644156245e97[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0xffff9f3603db3c18[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x8000[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x101[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x644156245e97[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffff9c (user space)[0m
[2m│  [0m[2m├─ Ptr[1]: [0m[37m0x644156245e97 (user space)[0m
[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x644156245e97 (user space)[0m
[1m├─ Stack ID: [0m[36m17[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.515930] [0m[34mredis-server:947/947[0m[2m on CPU 2[0m[31m [exit][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb144bc9b[0m[2m (__builtin__ftrace)[0m
[1m├─ Stack ID: [0m[36m260[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.516280] [0m[34mredis-server:947/947[0m[2m on CPU 2[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2fe8d709e8[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c2fe8d709e8[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m194[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.518327] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb144b825[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xc000426000[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x2f[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffff9f360a23bdf8[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xc000426000 (user space)[0m
[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.520026] [0m[34mswapper/6:0/0[0m[2m on CPU 6[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f80350000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffffffffb11e2ca0[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x100913932[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x20[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c3078da2128[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xffffffffb11e2ca0 -> process_timeout (kernel)[0m
[2m│  [0m[2m├─ Ptr[2]: [0m[37m0x100913932 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.520054] [0m[34mrcu_preempt:17/17[0m[2m on CPU 6[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x246[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffffffffb33a6ce0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c3078da2100[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[2m│  [0m[2m├─ Ptr[4]: [0m[37m0xffffffffb33a6ce0 (kernel space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.520676] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599a667c2[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599a667c2 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.520718] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.520738] [0m[34mtracer:18251/18254[0m[2m on CPU 4[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffff8c307306a208[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c307306a208[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c307306a208[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m894[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.520796] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599a8402b[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599a8402b (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.520811] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c3073069108[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c3073069108[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m12[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.521736] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb144b825[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xc0000c0280[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x41[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffff9f360a23bbc8[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xc0000c0280 (user space)[0m
[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.521742] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.521743] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[31m [exit][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1449279[0m[2m (__builtin__ftrace)[0m
[1m├─ Duration: [0m[32m8.643µs[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xc0000c0280[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x41[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffff9f360a23bbc8[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xc0000c0280 (user space)[0m
[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.521772] [0m[34mtracer:18251/18254[0m[2m on CPU 4[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffff8c307306a208[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c307306a208[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c307306a208[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m894[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.521824] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599b7ef2b[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599b7ef2b (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.521852] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.521874] [0m[34mtracer:18251/18254[0m[2m on CPU 4[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffff8c307306a208[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c307306a208[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c307306a208[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m894[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.521935] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599b99e27[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599b99e27 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.521944] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c3073069108[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c3073069108[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m12[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.522826] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb144b825[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xc0000c0280[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x34[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffff9f360a23bd20[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xc0000c0280 (user space)[0m
[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.522832] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[31m [exit][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1449279[0m[2m (__builtin__ftrace)[0m
[1m├─ Duration: [0m[32m6.185µs[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xc0000c0280[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x34[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffff9f360a23bd20[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xc0000c0280 (user space)[0m
[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.522862] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.522886] [0m[34mtracer:18251/18254[0m[2m on CPU 4[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffff8c307306a208[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c307306a208[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c307306a208[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m894[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.522946] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599c90aef[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599c90aef (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.522955] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.523037] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599ca7088[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599ca7088 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.523046] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.523128] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599cbd425[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599cbd425 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.523137] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.523219] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599cd3777[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599cd3777 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.523228] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.523310] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599ce9a84[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599ce9a84 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.523319] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.523401] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x16[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599d0001e[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599d0001e (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.523411] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.523493] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599d165fa[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599d165fa (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.523502] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.523593] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599d2c7a4[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599d2c7a4 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.523601] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.523681] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599d4464d[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599d4464d (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.523690] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.523770] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599d59ff9[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599d59ff9 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.523779] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.523868] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599d7191d[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599d7191d (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.523880] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.523971] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599d8b0e3[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599d8b0e3 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.523981] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.524050] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2fa989c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffff8c3078c32ae8[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x0[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffff8c3078c32ae8[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c3078c32ae8[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c3078c32ae8[0m[2m (R9)[0m
[1m├─ Memory:
[0m[1m├─ Stack ID: [0m[36m693[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.524053] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599d9ed90[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599d9ed90 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.524060] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f80344000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffffffffb29ab536[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x0[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffffffff[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x100[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xffffffffb29ab536 (kernel space)[0m
[2m│  [0m[2m├─ Ptr[3]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m693[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.524064] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.524155] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599db7d9c[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599db7d9c (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.524165] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.524247] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599dce8a3[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599dce8a3 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.524256] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.524337] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599de46a4[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599de46a4 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.524346] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.524432] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599dfb7bb[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599dfb7bb (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.524441] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.524523] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599e11dee[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599e11dee (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.524532] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.524664] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599e3233d[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599e3233d (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.524719] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.525032] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599e8d485[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599e8d485 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.525072] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.525197] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599eb5cbf[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599eb5cbf (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.525233] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.525358] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x12[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599edc82f[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599edc82f (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.525377] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.525464] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599ef7761[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599ef7761 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.525477] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.525563] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599f0fc1b[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599f0fc1b (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.525576] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.525662] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599f27c1f[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599f27c1f (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.525674] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.525762] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599f403ec[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599f403ec (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.525775] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.525864] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599f59064[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599f59064 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.525877] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.525968] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599f72482[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599f72482 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.525984] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.526074] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599f8c387[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599f8c387 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.526088] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.526173] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599fa492a[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599fa492a (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.526186] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.526272] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599fbca02[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599fbca02 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.526284] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.526370] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e599fd49b6[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e599fd49b6 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.526382] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.526417] [0m[34mkworker/0:2:17145/17145[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffffffffb326a630[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffffffffb326a630[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffffffffb326a630[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xffffffffb326a630 -> ftrace_trampoline (kernel)[0m
[2m│  [0m[2m├─ Ptr[4]: [0m[37m0xffffffffb326a630 -> ftrace_trampoline (kernel)[0m
[2m│  [0m[2m├─ Ptr[5]: [0m[37m0xffffffffb326a630 -> ftrace_trampoline (kernel)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.526427] [0m[34mksoftirqd/0:16/16[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffffffffb29ab536[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c30721a8040[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x100[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xffffffffb29ab536 (kernel space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.526573] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a0046df[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a0046df (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.526627] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.526767] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a034dd4[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a034dd4 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.526789] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.526802] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffffb2960dab[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffffffffb297d70e[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x3d[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2f9b384e60[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c2f9b384e60[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffffb2960dab (kernel space)[0m
[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xffffffffb297d70e (kernel space)[0m
[1m├─ Stack ID: [0m[36m693[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.526827] [0m[34mkworker/0:2:17145/17145[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffff8c3078c32b00[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c30728be600[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c30728be600[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.526864] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb144b825[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xc0000c0280[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x34[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffff9f360a23bc98[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xc0000c0280 (user space)[0m
[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.526869] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[31m [exit][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1449279[0m[2m (__builtin__ftrace)[0m
[1m├─ Duration: [0m[32m5.405µs[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xc0000c0280[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x34[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffff9f360a23bc98[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xc0000c0280 (user space)[0m
[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.526905] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a0565e4[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a0565e4 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.526937] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.527043] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a078bfe[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a078bfe (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.527069] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.527173] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a098852[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a098852 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.527199] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.527229] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0x7fffffffffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffff9f360a23b8d0[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff9f360a23b8d0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff9f360a23b8d0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[1m├─ Stack ID: [0m[36m106[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.527351] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x16[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a0c39ea[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a0c39ea (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.527373] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.527490] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a0e5c4e[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a0e5c4e (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.527503] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.527598] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a100711[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a100711 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.527611] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.527697] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a1189d2[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a1189d2 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.527708] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.527791] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a12f7c9[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a12f7c9 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.527801] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.527884] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a1464e9[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a1464e9 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.527895] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.527978] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a15d00f[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a15d00f (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.527987] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.528016] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a166bc3[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a166bc3 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.528022] [0m[34mswapper/6:0/0[0m[2m on CPU 6[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f80350000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffffffffb11e2ca0[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x100913934[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x20[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c3078da2128[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xffffffffb11e2ca0 -> process_timeout (kernel)[0m
[2m│  [0m[2m├─ Ptr[2]: [0m[37m0x100913934 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.528025] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.528041] [0m[34mrcu_preempt:17/17[0m[2m on CPU 6[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x246[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffffffffb33a6ce0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c3078da2100[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[2m│  [0m[2m├─ Ptr[4]: [0m[37m0xffffffffb33a6ce0 (kernel space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.528108] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a17ce1f[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a17ce1f (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.528118] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.528201] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a193a44[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a193a44 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.528212] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.528294] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a1aa678[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a1aa678 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.528305] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.528393] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a1c2085[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a1c2085 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.528402] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.528483] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a1d885b[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a1d885b (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.528492] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.528592] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a1f3261[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a1f3261 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.528601] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.528785] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a221fd8[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a221fd8 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.528801] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.529018] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a25b072[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a25b072 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.529030] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.529484] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x16[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a2ccb3a[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a2ccb3a (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.529499] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.530293] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a3925ca[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a3925ca (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.530306] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.531678] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a4e4430[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a4e4430 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.531692] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.534282] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59a76017f[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59a76017f (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.534298] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.536009] [0m[34mswapper/6:0/0[0m[2m on CPU 6[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f80350000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffffffffb11e2ca0[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x100913936[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x20[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c3078da2128[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xffffffffb11e2ca0 -> process_timeout (kernel)[0m
[2m│  [0m[2m├─ Ptr[2]: [0m[37m0x100913936 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.536020] [0m[34mrcu_preempt:17/17[0m[2m on CPU 6[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x246[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffffffffb33a6ce0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c3078da2100[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[2m│  [0m[2m├─ Ptr[4]: [0m[37m0xffffffffb33a6ce0 (kernel space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.539505] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59ac5a233[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59ac5a233 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.539570] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.539598] [0m[34mtracer:18251/18254[0m[2m on CPU 4[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffff8c307306a208[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c307306a208[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c307306a208[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m894[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.539661] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59ac817c2[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59ac817c2 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.539673] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c3073069108[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c3073069108[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m12[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.544026] [0m[34mswapper/6:0/0[0m[2m on CPU 6[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f80350000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffffffffb11e2ca0[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x100913938[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x20[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c3078da2128[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xffffffffb11e2ca0 -> process_timeout (kernel)[0m
[2m│  [0m[2m├─ Ptr[2]: [0m[37m0x100913938 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.544043] [0m[34mrcu_preempt:17/17[0m[2m on CPU 6[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x282[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffffffffb33a6ce0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffffffffb33a6ce0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[2m│  [0m[2m├─ Ptr[4]: [0m[37m0xffffffffb33a6ce0 (kernel space)[0m
[2m│  [0m[2m├─ Ptr[5]: [0m[37m0xffffffffb33a6ce0 (kernel space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.548169] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb144b825[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xc0000c0280[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x2d[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffff9f360a23bad8[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xc0000c0280 (user space)[0m
[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.548180] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[31m [exit][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1449279[0m[2m (__builtin__ftrace)[0m
[1m├─ Duration: [0m[32m12.077µs[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xc0000c0280[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x2d[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffff9f360a23bad8[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xc0000c0280 (user space)[0m
[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.548283] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.548315] [0m[34mtracer:18251/18260[0m[2m on CPU 2[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffff8c307307ce08[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c307307ce08[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c307307ce08[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m394[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.548317] [0m[34mtracer:18251/18254[0m[2m on CPU 4[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xffff8c307306a208[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c307306a208[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c307306a208[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m894[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.548364] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b4ce448[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b4ce448 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.548374] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.548454] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb144b825[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xc0001ac000[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0xc9[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffff9f360a23bc20[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xc0001ac000 (user space)[0m
[1m├─ Stack ID: [0m[36m156[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.548457] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b4e4e09[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b4e4e09 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.548459] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[31m [exit][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1449279[0m[2m (__builtin__ftrace)[0m
[1m├─ Duration: [0m[32m6.915µs[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xc0001ac000[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0xc9[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffff9f360a23bc20[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xc0001ac000 (user space)[0m
[1m├─ Stack ID: [0m[36m156[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.548466] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.548617] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b50bd2f[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b50bd2f (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.548630] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.548715] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b524095[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b524095 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.548725] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.548812] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b53b97d[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b53b97d (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.548825] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.548914] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b554898[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b554898 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.548923] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549001] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b569dec[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b569dec (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549009] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549044] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb144b825[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xc0001ac000[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x3e[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffff9f360a23be30[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xc0001ac000 (user space)[0m
[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549048] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb173ac45[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x3e[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x3e[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549049] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[31m [exit][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1449279[0m[2m (__builtin__ftrace)[0m
[1m├─ Duration: [0m[32m1.987µs[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x3e[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x3e[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549094] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b5803e9[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b5803e9 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549108] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549250] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b5a6746[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b5a6746 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549264] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549387] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x12[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b5c7ecd[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b5c7ecd (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549400] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549486] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b5e00ed[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b5e00ed (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549495] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549575] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b5f5e74[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b5f5e74 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549583] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549673] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b60da7e[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b60da7e (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549686] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549706] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb144b825[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xc0001ac000[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x31[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffff9f360a23bd70[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xc0001ac000 (user space)[0m
[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549709] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb173ac45[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x31[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x31[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549711] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[31m [exit][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1449279[0m[2m (__builtin__ftrace)[0m
[1m├─ Duration: [0m[32m2.168µs[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x31[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x31[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549765] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b624912[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b624912 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549775] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549880] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb144b825[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xc0001ac000[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x2f[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffff9f360a23bdf0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xc0001ac000 (user space)[0m
[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549881] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb173ac45[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x2f[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x2f[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549882] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[31m [exit][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1449279[0m[2m (__builtin__ftrace)[0m
[1m├─ Duration: [0m[32m1.329µs[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x2f[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x2f[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549898] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b6448a2[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b6448a2 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549911] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549953] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb144b825[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xc0001ac000[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0xc8[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffff9f360a23be58[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xc0001ac000 (user space)[0m
[1m├─ Stack ID: [0m[36m156[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549954] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb173ac45[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0xc8[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xc8[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[1m├─ Stack ID: [0m[36m156[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549955] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[31m [exit][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1449279[0m[2m (__builtin__ftrace)[0m
[1m├─ Duration: [0m[32m1.178µs[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0xc8[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xc8[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[1m├─ Stack ID: [0m[36m156[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.549996] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b65ca1e[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b65ca1e (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550006] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550131] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b67d8cf[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b67d8cf (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550144] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550176] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb144b825[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xc0001ac000[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x2e[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffff9f360a23be70[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xc0001ac000 (user space)[0m
[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550178] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb173ac45[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x2e[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x2e[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550179] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[31m [exit][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1449279[0m[2m (__builtin__ftrace)[0m
[1m├─ Duration: [0m[32m1.234µs[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x2e[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x2e[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550229] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b695803[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b695803 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550238] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550319] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b6abc09[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b6abc09 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550328] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550407] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b6c11bd[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b6c11bd (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550415] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550493] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b6d62ea[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b6d62ea (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550501] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550579] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b6eb2aa[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b6eb2aa (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550587] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550666] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b70046d[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b70046d (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550673] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550752] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b71545d[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b71545d (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550759] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550836] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b729dcf[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b729dcf (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550844] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550922] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x12[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b73ee5a[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b73ee5a (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.550930] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.551008] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b753f6a[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b753f6a (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.551016] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.551094] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b768e50[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b768e50 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.551102] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.551180] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b77dcf8[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b77dcf8 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.551187] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.551267] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b79314d[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b79314d (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.551274] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.551351] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b7a7b28[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b7a7b28 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.551357] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.551487] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b7c8d36[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b7c8d36 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.551497] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.551574] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b7de219[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b7de219 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.551581] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.551667] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b7f48e8[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b7f48e8 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.551680] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.551758] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b80b1b9[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b80b1b9 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.551766] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.551842] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b81fb47[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b81fb47 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.551849] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.551926] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b834148[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b834148 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.551932] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552013] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10102[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b8495eb[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b8495eb (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552020] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552049] [0m[34mrcu_preempt:17/17[0m[2m on CPU 6[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x246[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffffffffb33a6ce0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0xffff8c3078da2100[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[2m│  [0m[2m├─ Ptr[4]: [0m[37m0xffffffffb33a6ce0 (kernel space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552098] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b85e161[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b85e161 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552104] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552181] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b8726ab[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b8726ab (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552187] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552264] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b886bd1[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b886bd1 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552271] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552348] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x16[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b89b21c[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b89b21c (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552354] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552484] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b8bbfce[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b8bbfce (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552495] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552582] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b8d3e1c[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b8d3e1c (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552593] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552650] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb144b825[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xc0001ac000[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x31[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffff9f360a23bcd0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xc0001ac000 (user space)[0m
[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552653] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb173ac45[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x31[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x31[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552655] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[31m [exit][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1449279[0m[2m (__builtin__ftrace)[0m
[1m├─ Duration: [0m[32m2.355µs[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x31[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x31[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552676] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b8eb012[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b8eb012 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552685] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552769] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b901d12[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b901d12 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552779] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552866] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b9193bf[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b9193bf (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552877] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552956] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b92facd[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b92facd (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.552963] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.553041] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b9445db[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b9445db (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.553048] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.553126] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b959035[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b959035 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.553133] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.553211] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b96dcab[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b96dcab (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.553214] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb144b825[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xc0001ac000[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x34[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffff9f360a23bda0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xc0001ac000 (user space)[0m
[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.553216] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb173ac45[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x34[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x34[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.553218] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[31m [exit][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1449279[0m[2m (__builtin__ftrace)[0m
[1m├─ Duration: [0m[32m1.670µs[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x34[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x34[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.553218] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.553315] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b9873ea[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b9873ea (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.553322] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.553457] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b9a9ce7[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b9a9ce7 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.553464] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.553674] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x6[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59b9ded61[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59b9ded61 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.553681] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.554067] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59ba3ee33[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59ba3ee33 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.554075] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.554752] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59bae5941[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59bae5941 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.554766] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.555814] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb144b825[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xc0001ac000[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x2d[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffff9f360a23bbb8[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xc0001ac000 (user space)[0m
[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.555818] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb173ac45[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x2d[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x2d[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.555821] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[31m [exit][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1449279[0m[2m (__builtin__ftrace)[0m
[1m├─ Duration: [0m[32m2.783µs[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x2d[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x2d[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.556107] [0m[34mswapper/1:0/0[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1165615[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2ff512c000[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x16[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x10002[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0xffff8c2ff512c000[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x22e59bc309f4[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[5]: [0m[37m0x22e59bc309f4 (user space)[0m
[1m├─ Stack ID: [0m[36m18446744073709551602[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.556118] [0m[34mtracer:18251/18252[0m[2m on CPU 1[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb20866e5[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffffffff[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x283[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x1[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x0[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x0[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x10ee643ea698677[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[0]: [0m[37m0xffffffff (user space)[0m
[1m├─ Stack ID: [0m[36m896[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.556180] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb144b825[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xc0001ac000[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x32[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffff9f360a23bcf8[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xc0001ac000 (user space)[0m
[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.556183] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb173ac45[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x32[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x32[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.556184] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[31m [exit][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1449279[0m[2m (__builtin__ftrace)[0m
[1m├─ Duration: [0m[32m1.913µs[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x32[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x32[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.556186] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[31m [exit][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb144be1b[0m[2m (__builtin__ftrace)[0m
[1m├─ Stack ID: [0m[36m915[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.556190] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb144b825[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0xc0001ac000[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x3b[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0xffff9f360a23bd98[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[2m│  [0m[2m├─ Ptr[1]: [0m[37m0xc0001ac000 (user space)[0m
[1m├─ Stack ID: [0m[36m701[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.556191] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[32m [entry][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb173ac45[0m[2m (__builtin__ftrace)[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x3b[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x3b[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[1m├─ Stack ID: [0m[36m701[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.556193] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[31m [exit][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb1449279[0m[2m (__builtin__ftrace)[0m
[1m├─ Duration: [0m[32m1.376µs[0m
[1m├─ Arguments:
[0m[2m│  [0m[2m├─ [0] [0m[37marg0=0xffff8c2f98bb9600[0m[2m (RDI)[0m
[2m│  [0m[2m├─ [1] [0m[37marg1=0x2[0m[2m (RSI)[0m
[2m│  [0m[2m├─ [2] [0m[37marg2=0x3b[0m[2m (RDX)[0m
[2m│  [0m[2m├─ [3] [0m[37marg3=0x3b[0m[2m (RCX)[0m
[2m│  [0m[2m├─ [4] [0m[37marg4=0x1[0m[2m (R8)[0m
[2m│  [0m[2m├─ [5] [0m[37marg5=0x0[0m[2m (R9)[0m
[1m├─ Memory:
[0m[1m├─ Stack ID: [0m[36m701[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

[36m┌─ [17:39:29.556194] [0m[34mtracer:18251/18259[0m[2m on CPU 0[0m[31m [exit][0m
[1m├─ Function: [0m[37mftrace_trampoline[0m[2m[0m[33m [kernel][0m
[1m├─ Address:  [0m[36m0xffffffffb144be1b[0m[2m (__builtin__ftrace)[0m
[1m├─ Stack ID: [0m[36m701[0m[2m (call stack available)[0m
[2m└─────────────────────────────────────────────────────────────────────────────────[0m

