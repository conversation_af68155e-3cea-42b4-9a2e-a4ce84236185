package main

import "C"

import (
	"os"
	"runtime"
	"time"

	"encoding/binary"
	"fmt"
	"syscall"

	bpf "github.com/aquasecurity/libbpfgo"
)

func resizeMap(module *bpf.Module, name string, size uint32) error {
	m, err := module.GetMap("events")
	if err != nil {
		return err
	}

	if err = m.Resize(size); err != nil {
		return err
	}

	if actual := m.GetMaxEntries(); actual != size {
		return fmt.Errorf("map resize failed, expected %v, actual %v", size, actual)
	}

	return nil
}

func main() {
	bpfModule, err := bpf.NewModuleFromFile("main.bpf.o")
	if err != nil {
		fmt.Fprintln(os.Stderr, err)
		os.Exit(-1)
	}
	defer bpfModule.Close()

	if err = resizeMap(bpfModule, "events", 8192); err != nil {
		fmt.Fprintln(os.<PERSON>, err)
		os.Exit(-1)
	}

	bpfModule.BPFLoadObject()
	prog, err := bpfModule.GetProgram("kprobe__sys_mmap")
	if err != nil {
		fmt.Fprintln(os.Stderr, err)
		os.Exit(-1)
	}

	funcName := fmt.Sprintf("__%s_sys_mmap", ksymArch())
	_, err = prog.AttachKprobe(funcName)
	if err != nil {
		fmt.Fprintln(os.Stderr, err)
		os.Exit(-1)
	}

	eventsChannel := make(chan []byte)
	lostChannel := make(chan uint64)
	pb, err := bpfModule.InitPerfBuf("events", eventsChannel, lostChannel, 1)
	if err != nil {
		fmt.Fprintln(os.Stderr, err)
		os.Exit(-1)
	}

	pb.Poll(300)

	stop := make(chan struct{})

	go func() {
		for {
			select {
			case <-stop:
				return
			case b := <-eventsChannel:
				if binary.LittleEndian.Uint32(b) != 2021 {
					fmt.Fprintf(os.Stderr, "invalid data retrieved\n")
					os.Exit(-1)
				}
			}
		}
	}()

	// give some time for the upper goroutine to start
	time.Sleep(100 * time.Millisecond)

	for sent := 0; sent < 5; sent++ {
		syscall.Mmap(999, 999, 999, 1, 1)
		time.Sleep(100 * time.Millisecond)
	}

	close(stop)

	// Test that it won't cause a panic or block if Stop or Close called multiple times
	pb.Stop()
	pb.Stop()
	pb.Close()
	pb.Close()
	pb.Stop()
}

func ksymArch() string {
	switch runtime.GOARCH {
	case "amd64":
		return "x64"
	case "arm64":
		return "arm64"
	default:
		panic("unsupported architecture")
	}
}
