//go:build windows

package efw

// See https://github.com/microsoft/ebpf-for-windows/blob/main/include/ebpf_result.h
type Result int32

//go:generate go run golang.org/x/tools/cmd/stringer@latest -tags windows -output result_string_windows.go -type=Result

const (
	EBPF_SUCCESS Result = iota
	EBPF_VERIFICATION_FAILED
	EBPF_JIT_COMPILATION_FAILED
	EBPF_PROGRAM_LOAD_FAILED
	EBPF_INVALID_FD
	EBPF_INVALID_OBJECT
	EBPF_INVALID_ARGUMENT
	EBPF_OBJECT_NOT_FOUND
	EBPF_OBJECT_ALREADY_EXISTS
	EBPF_FILE_NOT_FOUND
	EBPF_ALREADY_PINNED
	EBPF_NOT_PINNED
	EBPF_NO_MEMORY
	EBPF_PROGRAM_TOO_LARGE
	EBPF_RPC_EXCEPTION
	EBPF_ALREADY_INITIALIZED
	EBPF_ELF_PARSING_FAILED
	EBPF_FAILED
	EBPF_OPERATION_NOT_SUPPORTED
	EBPF_KEY_NOT_FOUND
	EBPF_ACCESS_DENIED
	EBPF_BLOCKED_BY_POLICY
	EBPF_ARITHMETIC_OVERFLOW
	EBPF_EXTENSION_FAILED_TO_LOAD
	EBPF_INSUFFICIENT_BUFFER
	EBPF_NO_MORE_KEYS
	EBPF_KEY_ALREADY_EXISTS
	EBPF_NO_MORE_TAIL_CALLS
	EBPF_PENDING
	EBPF_OUT_OF_SPACE
	EBPF_CANCELED
	EBPF_INVALID_POINTER
	EBPF_TIMEOUT
	EBPF_STALE_ID
	EBPF_INVALID_STATE
)

func (r Result) Error() string {
	return r.String()
}

func resultToError(res Result) error {
	if res == EBPF_SUCCESS {
		return nil
	}
	return res
}
