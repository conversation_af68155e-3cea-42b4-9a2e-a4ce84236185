// Code generated by "stringer -output jump_string.go -type=JumpOp"; DO NOT EDIT.

package asm

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[InvalidJumpOp-255]
	_ = x[Ja-0]
	_ = x[JEq-16]
	_ = x[JGT-32]
	_ = x[JGE-48]
	_ = x[JSet-64]
	_ = x[JNE-80]
	_ = x[JSGT-96]
	_ = x[JSGE-112]
	_ = x[Call-128]
	_ = x[Exit-144]
	_ = x[JLT-160]
	_ = x[JLE-176]
	_ = x[JSLT-192]
	_ = x[JSLE-208]
}

const _JumpOp_name = "JaJEqJGTJGEJSetJNEJSGTJSGECallExitJLTJLEJSLTJSLEInvalidJumpOp"

var _JumpOp_map = map[JumpOp]string{
	0:   _JumpOp_name[0:2],
	16:  _JumpOp_name[2:5],
	32:  _JumpOp_name[5:8],
	48:  _JumpOp_name[8:11],
	64:  _JumpOp_name[11:15],
	80:  _JumpOp_name[15:18],
	96:  _JumpOp_name[18:22],
	112: _JumpOp_name[22:26],
	128: _JumpOp_name[26:30],
	144: _JumpOp_name[30:34],
	160: _JumpOp_name[34:37],
	176: _JumpOp_name[37:40],
	192: _JumpOp_name[40:44],
	208: _JumpOp_name[44:48],
	255: _JumpOp_name[48:61],
}

func (i JumpOp) String() string {
	if str, ok := _JumpOp_map[i]; ok {
		return str
	}
	return "JumpOp(" + strconv.FormatInt(int64(i), 10) + ")"
}
