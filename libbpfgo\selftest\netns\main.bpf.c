//+build ignore

#include <vmlinux.h>

#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>

struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, 1 << 24);
} events SEC(".maps");
long ringbuffer_flags = 0;

SEC("sk_lookup")
int sk_lookup__lookup(struct bpf_sk_lookup *ctx)
{
    int *process;

    // Reserve space on the ringbuffer for the sample
    process = bpf_ringbuf_reserve(&events, sizeof(int), ringbuffer_flags);
    if (!process) {
        return 1;
    }

    *process = 2021;

    bpf_ringbuf_submit(process, ringbuffer_flags);
    return 1;
}

char LICENSE[] SEC("license") = "GPL";
