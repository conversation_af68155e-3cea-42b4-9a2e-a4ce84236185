﻿// Code generated by internal/cmd/genwinfunctions.awk; DO NOT EDIT.

package asm

// Code in this file is derived from eBPF for Windows, available under the MIT License.

import (
	"github.com/cilium/ebpf/internal/platform"
)

// Built-in functions (Windows).
const (
	WindowsFnMapLookupElem          = BuiltinFunc(platform.WindowsTag | 1)
	WindowsFnMapUpdateElem          = BuiltinFunc(platform.WindowsTag | 2)
	WindowsFnMapDeleteElem          = BuiltinFunc(platform.WindowsTag | 3)
	WindowsFnMapLookupAndDeleteElem = BuiltinFunc(platform.WindowsTag | 4)
	WindowsFnTailCall               = BuiltinFunc(platform.WindowsTag | 5)
	WindowsFnGetPrandomU32          = BuiltinFunc(platform.WindowsTag | 6)
	WindowsFnKtimeGetBootNs         = BuiltinFunc(platform.WindowsTag | 7)
	WindowsFnGetSmpProcessorId      = BuiltinFunc(platform.WindowsTag | 8)
	WindowsFnKtimeGetNs             = BuiltinFunc(platform.WindowsTag | 9)
	WindowsFnCsumDiff               = BuiltinFunc(platform.WindowsTag | 10)
	WindowsFnRingbufOutput          = BuiltinFunc(platform.WindowsTag | 11)
	WindowsFnTracePrintk2           = BuiltinFunc(platform.WindowsTag | 12)
	WindowsFnTracePrintk3           = BuiltinFunc(platform.WindowsTag | 13)
	WindowsFnTracePrintk4           = BuiltinFunc(platform.WindowsTag | 14)
	WindowsFnTracePrintk5           = BuiltinFunc(platform.WindowsTag | 15)
	WindowsFnMapPushElem            = BuiltinFunc(platform.WindowsTag | 16)
	WindowsFnMapPopElem             = BuiltinFunc(platform.WindowsTag | 17)
	WindowsFnMapPeekElem            = BuiltinFunc(platform.WindowsTag | 18)
	WindowsFnGetCurrentPidTgid      = BuiltinFunc(platform.WindowsTag | 19)
	WindowsFnGetCurrentLogonId      = BuiltinFunc(platform.WindowsTag | 20)
	WindowsFnIsCurrentAdmin         = BuiltinFunc(platform.WindowsTag | 21)
	WindowsFnMemcpy                 = BuiltinFunc(platform.WindowsTag | 22)
	WindowsFnMemcmp                 = BuiltinFunc(platform.WindowsTag | 23)
	WindowsFnMemset                 = BuiltinFunc(platform.WindowsTag | 24)
	WindowsFnMemmove                = BuiltinFunc(platform.WindowsTag | 25)
	WindowsFnGetSocketCookie        = BuiltinFunc(platform.WindowsTag | 26)
	WindowsFnStrncpyS               = BuiltinFunc(platform.WindowsTag | 27)
	WindowsFnStrncatS               = BuiltinFunc(platform.WindowsTag | 28)
	WindowsFnStrnlenS               = BuiltinFunc(platform.WindowsTag | 29)
	WindowsFnKtimeGetBootMs         = BuiltinFunc(platform.WindowsTag | 30)
	WindowsFnKtimeGetMs             = BuiltinFunc(platform.WindowsTag | 31)
)
