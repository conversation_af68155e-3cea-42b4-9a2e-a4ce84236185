// Code generated by "stringer -output opcode_string.go -type=Class"; DO NOT EDIT.

package asm

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[LdClass-0]
	_ = x[LdXClass-1]
	_ = x[StClass-2]
	_ = x[StXClass-3]
	_ = x[ALUClass-4]
	_ = x[JumpClass-5]
	_ = x[Jump32Class-6]
	_ = x[ALU64Class-7]
}

const _Class_name = "LdClassLdXClassStClassStXClassALUClassJumpClassJump32ClassALU64Class"

var _Class_index = [...]uint8{0, 7, 15, 22, 30, 38, 47, 58, 68}

func (i Class) String() string {
	if i >= Class(len(_Class_index)-1) {
		return "Class(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _Class_name[_Class_index[i]:_Class_index[i+1]]
}
