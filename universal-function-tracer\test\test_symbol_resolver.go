// Test program to verify BCC-style symbol resolution works
package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"

	"github.com/mexyusef/universal-function-tracer/pkg/utils"
)

func main() {
	fmt.Println("=== BCC-Style Symbol Resolution Test ===")
	
	resolver := utils.NewBCCSymbolResolver()
	
	// Test 1: Resolve symbols in our C hello world program
	helloWorldPath := "./hello-world"
	absPath, _ := filepath.Abs(helloWorldPath)
	fmt.Printf("Testing C program: %s\n", absPath)
	
	// Check if the binary exists
	if _, err := os.Stat(helloWorldPath); os.IsNotExist(err) {
		log.Printf("C hello world binary not found at %s, skipping C tests", helloWorldPath)
	} else {
		testSymbols := []string{"main", "my_hello_function", "my_calculation_function", "my_memory_function"}
		
		for _, symbol := range testSymbols {
			fmt.Printf("\nResolving symbol: %s\n", symbol)
			offset, err := resolver.ResolveSymbolOffset(helloWorldPath, symbol)
			if err != nil {
				fmt.Printf("  ❌ Failed: %v\n", err)
			} else {
				fmt.Printf("  ✅ Success: offset 0x%x\n", offset)
			}
			
			// Get detailed symbol info
			if info, err := resolver.GetSymbolInfo(helloWorldPath, symbol); err == nil {
				fmt.Printf("  📋 Info: %+v\n", info)
			}
		}
		
		// List all symbols in the binary
		fmt.Printf("\nAll symbols in %s:\n", helloWorldPath)
		if symbols, err := resolver.ListSymbols(helloWorldPath); err == nil {
			for i, symbol := range symbols {
				if i < 20 { // Show first 20 symbols
					fmt.Printf("  - %s\n", symbol)
				}
			}
			if len(symbols) > 20 {
				fmt.Printf("  ... and %d more symbols\n", len(symbols)-20)
			}
		}
	}
	
	// Test 2: Resolve symbols in libc (dynamic library)
	fmt.Printf("\n=== Testing libc symbols ===\n")
	libcPath := "/lib/x86_64-linux-gnu/libc.so.6"
	libcSymbols := []string{"printf", "puts", "malloc", "free"}
	
	for _, symbol := range libcSymbols {
		fmt.Printf("\nResolving libc symbol: %s\n", symbol)
		offset, err := resolver.ResolveSymbolOffset(libcPath, symbol)
		if err != nil {
			fmt.Printf("  ❌ Failed: %v\n", err)
		} else {
			fmt.Printf("  ✅ Success: offset 0x%x\n", offset)
		}
	}
	
	fmt.Println("\n=== Symbol Resolution Test Complete ===")
}
