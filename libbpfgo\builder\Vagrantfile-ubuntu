ARCH = ENV["ARCH"] || "amd64"
HOSTOS = ENV["HOSTOS"] || "undefined"

if ! %w(amd64 arm64).include?(ARCH) then
    puts "ERROR: ARCH #{ARCH} not supported"
    abort
end

if ! %w(<PERSON> Darwin).include?(HOSTOS) then
    puts "ERROR: HOSTOS #{HOSTOS} not supported"
    abort
end

VM_NAME = "libbpfgo-#{ARCH}-vm"
VM_SOURCE = "/vagrant"

Vagrant.configure("2") do |config|
  case ARCH
  when "amd64"
    config.vm.box = "bento/ubuntu-24.04"
  when "arm64"
    config.vm.box = "bento/ubuntu-24.04"
  end

  case HOSTOS
  when "Linux"
    config.vm.provider "virtualbox" do |vb|
      vb.name = VM_NAME
      vb.cpus = "4"
      vb.memory = "2048"
    end
  when "Darwin"
    config.vm.provider "parallels" do |prl|
      prl.name = VM_NAME
    end
  end

  config.vm.hostname = VM_NAME
  config.vm.synced_folder "./", "#{VM_SOURCE}"

  config.ssh.extra_args = ["-t", "cd #{VM_SOURCE}; bash --login"]

  config.vm.provision :shell, inline: "echo INFO: Starting Provision"
  config.vm.provision :shell, inline: "ARCH=#{ARCH} #{VM_SOURCE}/builder/prepare-ubuntu.sh"
  config.vm.provision :shell, inline: "echo INFO: Provision finished, now connect via ssh"
end
