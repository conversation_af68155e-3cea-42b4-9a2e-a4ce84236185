// Package ebpf provides eBPF program loading and management functionality.
// This implementation uses cilium/ebpf with tracee-inspired symbol resolution.
package ebpf

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"github.com/cilium/ebpf"
	"github.com/cilium/ebpf/link"
	"github.com/cilium/ebpf/ringbuf"
	"github.com/cilium/ebpf/rlimit"
	"github.com/mexyusef/universal-function-tracer/pkg/events"
	"github.com/mexyusef/universal-function-tracer/pkg/utils"
)

// Helper function for min
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// UprobeConfig defines configuration for attaching uprobes
type UprobeConfig struct {
	Binary string // Path to binary (e.g., "/lib/x86_64-linux-gnu/libc.so.6")
	Symbol string // Symbol name (e.g., "malloc")
}

// USDTConfig defines configuration for attaching USDT probes
type USDTConfig struct {
	Binary   string // Path to binary containing USDT probes (e.g., "/usr/bin/python3")
	Provider string // USDT provider name (e.g., "python")
	Name     string // USDT probe name (e.g., "function__entry")
}

// Loader manages eBPF program loading and event processing using cilium/ebpf.
type Loader struct {
	spec        *ebpf.CollectionSpec
	collection  *ebpf.Collection
	links       []link.Link
	ringBuffer  *ringbuf.Reader
	decoder     *EventDecoder
	eventChan   chan *events.FunctionEvent
	stopChan    chan struct{}
	isRunning   bool
	stackCache  *events.StackTraceCache
	resolver    events.SymbolResolver
}

// NewLoader creates a new eBPF program loader using cilium/ebpf.
func NewLoader() *Loader {
	return &Loader{
		links:      make([]link.Link, 0),
		decoder:    NewEventDecoder(),
		eventChan:  make(chan *events.FunctionEvent, 1000), // Buffer 1000 events
		stopChan:   make(chan struct{}),
		stackCache: events.NewStackTraceCache(1000), // Cache up to 1000 stack traces
		resolver:   events.NewBasicSymbolResolver(),
	}
}

// LoadProgram loads the eBPF program from the specified object file using cilium/ebpf.
func (l *Loader) LoadProgram(objectPath string) error {
	// Remove memory limit for eBPF
	if err := rlimit.RemoveMemlock(); err != nil {
		return fmt.Errorf("failed to remove memory limit: %w", err)
	}

	// Load the eBPF program specification
	spec, err := ebpf.LoadCollectionSpec(objectPath)
	if err != nil {
		return fmt.Errorf("failed to load eBPF spec: %w", err)
	}
	l.spec = spec

	// Load the eBPF program collection
	collection, err := ebpf.NewCollection(spec)
	if err != nil {
		return fmt.Errorf("failed to create eBPF collection: %w", err)
	}
	l.collection = collection

	// Log available programs
	log.Printf("Available eBPF programs:")
	for progName := range collection.Programs {
		log.Printf("  - %s", progName)
	}

	// Initialize the ring buffer reader
	eventsMap, exists := collection.Maps["events"]
	if !exists {
		return fmt.Errorf("events ring buffer map not found")
	}

	ringBuffer, err := ringbuf.NewReader(eventsMap)
	if err != nil {
		return fmt.Errorf("failed to create ring buffer reader: %w", err)
	}
	l.ringBuffer = ringBuffer

	log.Printf("Successfully loaded eBPF program with %d programs and %d maps",
		len(collection.Programs), len(collection.Maps))

	return nil
}

// handleEvent processes events from the ring buffer
func (l *Loader) handleEvent(data []byte) {
	// Decode the event
	event, err := l.decoder.DecodeEvent(data)
	if err != nil {
		log.Printf("Error decoding event: %v", err)
		return
	}

	// Send to event channel
	select {
	case l.eventChan <- event:
	default:
		log.Printf("Warning: event channel full, dropping event")
	}
}

// AttachPrograms attaches the eBPF programs to their respective kernel hooks.
func (l *Loader) AttachPrograms() error {
	if l.collection == nil {
		return fmt.Errorf("no eBPF collection loaded")
	}

	// Attach kprobe for do_sys_openat2 entry
	if prog, exists := l.collection.Programs["trace_do_sys_openat2_entry"]; exists {
		link, err := link.Kprobe("do_sys_openat2", prog, nil)
		if err != nil {
			return fmt.Errorf("failed to attach kprobe do_sys_openat2_entry: %w", err)
		}
		l.links = append(l.links, link)
		log.Printf("Attached kprobe: do_sys_openat2 (entry)")
	}

	// Attach kretprobe for do_sys_openat2 exit
	if prog, exists := l.collection.Programs["trace_do_sys_openat2_exit"]; exists {
		link, err := link.Kretprobe("do_sys_openat2", prog, nil)
		if err != nil {
			return fmt.Errorf("failed to attach kretprobe do_sys_openat2_exit: %w", err)
		}
		l.links = append(l.links, link)
		log.Printf("Attached kretprobe: do_sys_openat2 (exit)")
	}

	// Attach kprobe for schedule entry (optional, may generate many events)
	if prog, exists := l.collection.Programs["trace_schedule_entry"]; exists {
		link, err := link.Kprobe("schedule", prog, nil)
		if err != nil {
			log.Printf("Warning: failed to attach kprobe schedule_entry: %v", err)
			// Don't fail completely if schedule attachment fails
		} else {
			l.links = append(l.links, link)
			log.Printf("Attached kprobe: schedule (entry)")
		}
	}

	// Attach kprobe for wake_up_process entry
	if prog, exists := l.collection.Programs["trace_wake_up_process_entry"]; exists {
		link, err := link.Kprobe("wake_up_process", prog, nil)
		if err != nil {
			log.Printf("Warning: failed to attach kprobe wake_up_process_entry: %v", err)
			// Don't fail completely if wake_up_process attachment fails
		} else {
			l.links = append(l.links, link)
			log.Printf("Attached kprobe: wake_up_process (entry)")
		}
	}

	// Try to attach additional kprobes (network, memory, process management)
	additionalKprobes := map[string]string{
		"trace_tcp_sendmsg_entry":              "tcp_sendmsg",
		"trace_tcp_sendmsg_exit":               "tcp_sendmsg",
		"trace_tcp_recvmsg_entry":              "tcp_recvmsg",
		"trace_tcp_recvmsg_exit":               "tcp_recvmsg",
		"trace_udp_sendmsg_entry":              "udp_sendmsg",
		"trace_udp_sendmsg_exit":               "udp_sendmsg",
		"trace_do_mmap_entry":                  "do_mmap",
		"trace_do_mmap_exit":                   "do_mmap",
		"trace_do_munmap_entry":                "do_munmap",
		"trace_do_munmap_exit":                 "do_munmap",
		"trace_do_fork_entry":                  "do_fork",
		"trace_do_fork_exit":                   "do_fork",
		"trace_do_exit_entry":                  "do_exit",
		"trace_vfs_read_entry":                 "vfs_read",
		"trace_vfs_read_exit":                  "vfs_read",
		"trace_vfs_write_entry":                "vfs_write",
		"trace_vfs_write_exit":                 "vfs_write",
		"trace_security_file_permission_entry": "security_file_permission",
		"trace_security_file_permission_exit":  "security_file_permission",
	}

	for progName, symbol := range additionalKprobes {
		if prog, exists := l.collection.Programs[progName]; exists {
			var err error
			var lnk link.Link

			if strings.HasSuffix(progName, "_exit") {
				lnk, err = link.Kretprobe(symbol, prog, nil)
			} else {
				lnk, err = link.Kprobe(symbol, prog, nil)
			}

			if err != nil {
				log.Printf("Warning: failed to attach %s to %s: %v", progName, symbol, err)
			} else {
				l.links = append(l.links, lnk)
				log.Printf("Attached %s: %s",
					map[bool]string{true: "kretprobe", false: "kprobe"}[strings.HasSuffix(progName, "_exit")],
					symbol)
			}
		}
	}

	if len(l.links) == 0 {
		return fmt.Errorf("no programs were successfully attached")
	}

	// CRITICAL: Attach uprobes for REAL USER FUNCTIONS
	// This is what will capture actual function names instead of ftrace_trampoline
	if err := l.attachUprobes(); err != nil {
		log.Printf("Warning: failed to attach uprobes: %v", err)
		// Don't fail completely if uprobes fail
	}

	log.Printf("Successfully attached %d eBPF programs", len(l.links))
	return nil
}

// attachUprobes attaches uprobes to capture real user function names
func (l *Loader) attachUprobes() error {
	// THE REAL SOLUTION: USERLAND FUNCTION UPROBES (based on ebpf-internet.md research)
	// Target actual userland functions with proper symbol resolution

	// BCC-STYLE USERLAND FUNCTION UPROBES - FOCUS ON C PROGRAMS FIRST
	// Based on research: start with simple C programs, then extend to other languages
	userlandUprobes := map[string]UprobeConfig{
		// Test with our C hello world program first (static symbols, easy to resolve)
		"trace_c_hello_main": {
			Binary: "./test/hello-world",  // Our C test program
			Symbol: "main",               // C main function
		},
		"trace_c_hello_function": {
			Binary: "./test/hello-world",  // Our C test program
			Symbol: "my_hello_function",  // Our custom C function
		},
		"trace_c_calculation_function": {
			Binary: "./test/hello-world",  // Our C test program
			Symbol: "my_calculation_function",  // Our custom C function
		},
		// Libc functions (dynamic symbols, harder but proven to work)
		"trace_userland_printf_entry": {
			Binary: "/lib/x86_64-linux-gnu/libc.so.6",
			Symbol: "printf",  // This is userland printf, not kernel
		},
		"trace_userland_puts_entry": {
			Binary: "/lib/x86_64-linux-gnu/libc.so.6",
			Symbol: "puts",   // This captures printf calls
		},
	}

	// Attach userland function uprobes
	for progName, config := range userlandUprobes {
		if err := l.attachUprobe(progName, config); err != nil {
			log.Printf("Warning: failed to attach userland uprobe %s: %v", config.Symbol, err)
		}
	}

	// Selective uprobes for native code only (when symbols are available)
	uprobes := map[string]UprobeConfig{
		"trace_malloc_entry": {
			Binary: "/lib/x86_64-linux-gnu/libc.so.6",
			Symbol: "malloc",
		},
		"trace_write_entry": {
			Binary: "/lib/x86_64-linux-gnu/libc.so.6",
			Symbol: "write",
		},
	}

	// Attach each uprobe
	for progName, config := range uprobes {
		if err := l.attachUprobe(progName, config); err != nil {
			log.Printf("Warning: failed to attach uprobe %s: %v", progName, err)
			// Continue with other uprobes even if one fails
		}
	}

	return nil
}

// attachUprobe attaches a single uprobe using tracee's symbol resolution with cilium/ebpf
func (l *Loader) attachUprobe(progName string, config UprobeConfig) error {
	// Get the eBPF program
	prog, exists := l.collection.Programs[progName]
	if !exists {
		return fmt.Errorf("program %s not found", progName)
	}

	// Check if binary exists
	if _, err := os.Stat(config.Binary); os.IsNotExist(err) {
		return fmt.Errorf("binary %s not found", config.Binary)
	}

	// CRITICAL: Use BCC-style symbol resolution (based on research)
	resolver := utils.NewBCCSymbolResolver()
	offset, err := resolver.ResolveSymbolOffset(config.Binary, config.Symbol)
	if err != nil {
		// Log detailed error information for debugging
		log.Printf("Symbol resolution failed for %s:%s - %v", config.Binary, config.Symbol, err)

		// Try to get symbol info for debugging
		if info, infoErr := resolver.GetSymbolInfo(config.Binary, config.Symbol); infoErr == nil {
			log.Printf("Symbol info: %+v", info)
		}

		// List available symbols for debugging
		if symbols, listErr := resolver.ListSymbols(config.Binary); listErr == nil {
			log.Printf("Available symbols in %s: %v", config.Binary, symbols[:min(10, len(symbols))])
		}

		return fmt.Errorf("BCC-style symbol resolution failed for %s:%s - %v", config.Binary, config.Symbol, err)
	}

	log.Printf("BCC-style symbol resolution successful: %s:%s -> offset 0x%x", config.Binary, config.Symbol, offset)

	// Attach uprobe or uretprobe based on program name using cilium/ebpf
	var lnk link.Link

	if strings.Contains(progName, "_exit") {
		// This is a uretprobe (function exit)
		ex, err := link.OpenExecutable(config.Binary)
		if err != nil {
			return fmt.Errorf("failed to open executable %s: %w", config.Binary, err)
		}
		lnk, err = ex.Uretprobe(config.Symbol, prog, &link.UprobeOptions{
			Offset: uint64(offset),
		})
		if err != nil {
			return fmt.Errorf("failed to attach uretprobe %s: %w", config.Symbol, err)
		}
		log.Printf("Attached uretprobe: %s (%s) at offset 0x%x", config.Symbol, config.Binary, offset)
	} else {
		// This is a uprobe (function entry)
		ex, err := link.OpenExecutable(config.Binary)
		if err != nil {
			return fmt.Errorf("failed to open executable %s: %w", config.Binary, err)
		}
		lnk, err = ex.Uprobe(config.Symbol, prog, &link.UprobeOptions{
			Offset: uint64(offset),
		})
		if err != nil {
			return fmt.Errorf("failed to attach uprobe %s: %w", config.Symbol, err)
		}
		log.Printf("Attached uprobe: %s (%s) at offset 0x%x", config.Symbol, config.Binary, offset)
	}

	l.links = append(l.links, lnk)
	return nil
}

// attachTracepoint attaches a tracepoint (much more reliable than uprobes)
func (l *Loader) attachTracepoint(progName string, tracepointName string) error {
	// Get the eBPF program
	prog, exists := l.collection.Programs[progName]
	if !exists {
		return fmt.Errorf("program %s not found", progName)
	}

	// Parse tracepoint name (format: "category:name")
	parts := strings.Split(tracepointName, ":")
	if len(parts) != 2 {
		return fmt.Errorf("invalid tracepoint name format: %s", tracepointName)
	}

	category := parts[0]
	name := parts[1]

	// Attach tracepoint using cilium/ebpf
	lnk, err := link.Tracepoint(category, name, prog, nil)
	if err != nil {
		return fmt.Errorf("failed to attach tracepoint %s: %w", tracepointName, err)
	}

	log.Printf("Attached tracepoint: %s", tracepointName)
	l.links = append(l.links, lnk)
	return nil
}

// attachUSDT attaches a USDT probe for userland function tracing
func (l *Loader) attachUSDT(progName string, config USDTConfig) error {
	// Get the eBPF program
	prog, exists := l.collection.Programs[progName]
	if !exists {
		return fmt.Errorf("program %s not found", progName)
	}

	// Check if binary exists
	if _, err := os.Stat(config.Binary); os.IsNotExist(err) {
		return fmt.Errorf("binary %s not found", config.Binary)
	}

	// For now, use uprobe-style attachment for USDT probes
	// TODO: Implement proper USDT support when cilium/ebpf adds it
	ex, err := link.OpenExecutable(config.Binary)
	if err != nil {
		return fmt.Errorf("failed to open executable %s: %w", config.Binary, err)
	}

	// Try to attach as uprobe with USDT probe name
	lnk, err := ex.Uprobe(config.Name, prog, nil)
	if err != nil {
		return fmt.Errorf("failed to attach USDT probe %s:%s: %w", config.Provider, config.Name, err)
	}

	log.Printf("Attached USDT probe: %s:%s (%s)", config.Provider, config.Name, config.Binary)
	l.links = append(l.links, lnk)
	return nil
}

// StartEventProcessing starts the event processing loop.
func (l *Loader) StartEventProcessing(ctx context.Context) error {
	if l.ringBuffer == nil {
		return fmt.Errorf("ring buffer not initialized")
	}

	l.isRunning = true
	go l.eventProcessingLoop(ctx)

	log.Printf("Started event processing loop")
	return nil
}

// eventProcessingLoop processes events from the ring buffer.
func (l *Loader) eventProcessingLoop(ctx context.Context) {
	defer func() {
		l.isRunning = false
		close(l.eventChan)
	}()

	for {
		select {
		case <-ctx.Done():
			log.Printf("Event processing loop stopped by context")
			return
		case <-l.stopChan:
			log.Printf("Event processing loop stopped by stop signal")
			return
		default:
			// Read from ring buffer with timeout
			record, err := l.ringBuffer.Read()
			if err != nil {
				if err == ringbuf.ErrClosed {
					log.Printf("Ring buffer closed")
					return
				}
				log.Printf("Error reading from ring buffer: %v", err)
				continue
			}

			// Decode the event
			event, err := l.decoder.DecodeEvent(record.RawSample)
			if err != nil {
				log.Printf("Error decoding event: %v", err)
				continue
			}

			// Send event to channel (non-blocking)
			select {
			case l.eventChan <- event:
				// Event sent successfully
			default:
				// Channel is full, drop the event
				log.Printf("Warning: event channel full, dropping event")
			}
		}
	}
}

// GetEventChannel returns the channel for receiving decoded events.
func (l *Loader) GetEventChannel() <-chan *events.FunctionEvent {
	return l.eventChan
}

// GetStats returns loader statistics.
func (l *Loader) GetStats() (eventsDecoded int64, errorsCount int64) {
	return l.decoder.GetStats()
}

// IsRunning returns true if the event processing loop is running.
func (l *Loader) IsRunning() bool {
	return l.isRunning
}

// GetStackTrace retrieves and decodes a stack trace by ID.
func (l *Loader) GetStackTrace(stackID uint64) (*events.StackTrace, error) {
	if stackID == 0 {
		return nil, fmt.Errorf("invalid stack ID: 0")
	}

	// Check cache first
	if trace, exists := l.stackCache.Get(stackID); exists {
		return trace, nil
	}

	// Get stack traces map
	stackMap, exists := l.collection.Maps["stack_traces"]
	if !exists {
		return nil, fmt.Errorf("stack_traces map not found")
	}

	// Lookup stack trace data
	var stackData []byte
	err := stackMap.Lookup(uint32(stackID), &stackData)
	if err != nil {
		return nil, fmt.Errorf("failed to lookup stack trace %d: %w", stackID, err)
	}

	// Decode stack trace
	trace, err := events.DecodeStackTrace(stackID, stackData)
	if err != nil {
		return nil, fmt.Errorf("failed to decode stack trace %d: %w", stackID, err)
	}

	// Resolve symbols
	if err := events.ResolveStackTrace(trace, l.resolver); err != nil {
		log.Printf("Warning: failed to resolve symbols for stack trace %d: %v", stackID, err)
	}

	// Cache the result
	l.stackCache.Put(trace)

	return trace, nil
}

// Stop stops the event processing and cleans up resources.
func (l *Loader) Stop() error {
	log.Printf("Stopping eBPF loader...")

	// Signal the event processing loop to stop
	if l.isRunning {
		close(l.stopChan)
		// Wait a bit for the loop to stop gracefully
		time.Sleep(100 * time.Millisecond)
	}

	// Close ring buffer
	if l.ringBuffer != nil {
		if err := l.ringBuffer.Close(); err != nil {
			log.Printf("Error closing ring buffer: %v", err)
		}
	}

	// Detach all programs
	for i, link := range l.links {
		if err := link.Close(); err != nil {
			log.Printf("Error closing link %d: %v", i, err)
		}
	}
	l.links = l.links[:0]

	// Close eBPF collection
	if l.collection != nil {
		l.collection.Close()
	}

	log.Printf("eBPF loader stopped successfully")
	return nil
}

// PrintDebugInfo prints debug information about the loaded programs and maps.
func (l *Loader) PrintDebugInfo() {
	if l.collection == nil {
		fmt.Println("No eBPF collection loaded")
		return
	}

	fmt.Printf("=== eBPF Debug Information ===\n")
	fmt.Printf("Programs (%d):\n", len(l.collection.Programs))
	for name, prog := range l.collection.Programs {
		fmt.Printf("  - %s (type: %s)\n", name, prog.Type())
	}

	fmt.Printf("Maps (%d):\n", len(l.collection.Maps))
	for name, m := range l.collection.Maps {
		fmt.Printf("  - %s (type: %s, max_entries: %d)\n", name, m.Type(), m.MaxEntries())
	}

	fmt.Printf("Links (%d):\n", len(l.links))
	for i, link := range l.links {
		fmt.Printf("  - Link %d: %T\n", i, link)
	}

	eventsDecoded, errorsCount := l.GetStats()
	fmt.Printf("Statistics:\n")
	fmt.Printf("  - Events decoded: %d\n", eventsDecoded)
	fmt.Printf("  - Errors: %d\n", errorsCount)
	fmt.Printf("  - Running: %t\n", l.IsRunning())
	fmt.Printf("===============================\n")
}
